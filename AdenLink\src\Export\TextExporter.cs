using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using AdenLink.Core;
using AdenLink.Database;

namespace AdenLink.Export
{
    public class TextExporter
    {
        private DatabaseManager dbManager;
        private ConfigManager configManager;

        public TextExporter()
        {
            dbManager = new DatabaseManager();
            configManager = ConfigManager.Instance;
        }

        /// <summary>
        /// تصدير دفعة البطاقات إلى ملف نصي
        /// </summary>
        public string ExportBatchToText(string batchId, string outputPath = null, TextFormat format = TextFormat.Detailed)
        {
            try
            {
                var cardGenerator = new CardGenerator();
                var batch = cardGenerator.GetAllBatches().Find(b => b.BatchId == batchId);
                var cards = cardGenerator.GetBatchCards(batchId);

                if (batch == null || cards == null || cards.Count == 0)
                {
                    throw new Exception("لم يتم العثور على الدفعة أو البطاقات");
                }

                // تحديد مسار الإخراج
                if (string.IsNullOrEmpty(outputPath))
                {
                    string exportDir = Path.Combine(configManager.ExportPath, "Text");
                    Directory.CreateDirectory(exportDir);
                    string formatSuffix = format.ToString().ToLower();
                    outputPath = Path.Combine(exportDir, $"{batch.BatchName}_{formatSuffix}_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                }

                // إنشاء المحتوى حسب التنسيق المطلوب
                string content = format switch
                {
                    TextFormat.Detailed => CreateDetailedFormat(batch, cards),
                    TextFormat.CSV => CreateCSVFormat(batch, cards),
                    TextFormat.Simple => CreateSimpleFormat(batch, cards),
                    TextFormat.MikroTik => CreateMikroTikFormat(batch, cards),
                    TextFormat.JSON => CreateJSONFormat(batch, cards),
                    _ => CreateDetailedFormat(batch, cards)
                };

                // كتابة الملف بترميز UTF-8 لدعم العربية
                File.WriteAllText(outputPath, content, Encoding.UTF8);

                dbManager.LogOperation("تصدير نص", $"تم تصدير الدفعة {batch.BatchName} إلى نص ({format}): {outputPath}");
                return outputPath;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تصدير النص: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير بطاقات متعددة بتنسيقات مختلفة
        /// </summary>
        public Dictionary<TextFormat, string> ExportBatchToMultipleFormats(string batchId, string outputDir = null)
        {
            var results = new Dictionary<TextFormat, string>();

            try
            {
                if (string.IsNullOrEmpty(outputDir))
                {
                    outputDir = Path.Combine(configManager.ExportPath, "Text");
                    Directory.CreateDirectory(outputDir);
                }

                var formats = new[] { TextFormat.Detailed, TextFormat.CSV, TextFormat.Simple, TextFormat.MikroTik };

                foreach (var format in formats)
                {
                    try
                    {
                        string outputPath = ExportBatchToText(batchId, null, format);
                        results[format] = outputPath;
                    }
                    catch (Exception ex)
                    {
                        dbManager.LogOperation("خطأ", $"فشل في تصدير التنسيق {format}: {ex.Message}");
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في التصدير المتعدد: {ex.Message}");
                throw;
            }
        }

        private string CreateDetailedFormat(CardGenerator.BatchInfo batch, List<CardGenerator.CardInfo> cards)
        {
            var sb = new StringBuilder();

            // رأس التقرير
            sb.AppendLine("========================================");
            sb.AppendLine($"           {configManager.CompanyName}");
            sb.AppendLine("========================================");
            sb.AppendLine("              تقرير بطاقات الشحن");
            sb.AppendLine("========================================");
            sb.AppendLine();

            // معلومات الدفعة
            sb.AppendLine("معلومات الدفعة:");
            sb.AppendLine("----------------");
            sb.AppendLine($"اسم الدفعة: {batch.BatchName}");
            sb.AppendLine($"معرف الدفعة: {batch.BatchId}");
            sb.AppendLine($"عدد البطاقات: {batch.TotalCards:N0}");
            sb.AppendLine($"قيمة البطاقة: {batch.CardValue:N2} {batch.Currency}");
            sb.AppendLine($"القيمة الإجمالية: {(batch.TotalCards * batch.CardValue):N2} {batch.Currency}");
            sb.AppendLine($"تاريخ الإنشاء: {batch.CreatedDate:dd/MM/yyyy HH:mm}");
            sb.AppendLine($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}");
            sb.AppendLine();

            // قائمة البطاقات
            sb.AppendLine("قائمة البطاقات:");
            sb.AppendLine("================");
            sb.AppendLine();

            int counter = 1;
            foreach (var card in cards)
            {
                sb.AppendLine($"البطاقة رقم: {counter}");
                sb.AppendLine($"الكود: {card.CardCode}");
                sb.AppendLine($"اسم المستخدم: {card.Username}");
                sb.AppendLine($"كلمة المرور: {card.Password}");
                sb.AppendLine($"القيمة: {card.Value:N2} {card.Currency}");
                sb.AppendLine($"تاريخ الإنشاء: {card.CreatedDate:dd/MM/yyyy HH:mm}");
                sb.AppendLine("----------------------------------------");
                counter++;
            }

            // تذييل التقرير
            sb.AppendLine();
            sb.AppendLine("========================================");
            sb.AppendLine($"تم إنشاء هذا التقرير بواسطة {configManager.AppName}");
            sb.AppendLine($"المستخدم: {Environment.UserName}");
            sb.AppendLine($"الوقت: {DateTime.Now:dd/MM/yyyy HH:mm:ss}");
            sb.AppendLine("========================================");

            return sb.ToString();
        }

        private string CreateCSVFormat(CardGenerator.BatchInfo batch, List<CardGenerator.CardInfo> cards)
        {
            var sb = new StringBuilder();

            // رأس CSV
            sb.AppendLine("# تقرير بطاقات الشحن - تنسيق CSV");
            sb.AppendLine($"# الدفعة: {batch.BatchName}");
            sb.AppendLine($"# تاريخ الإنشاء: {DateTime.Now:dd/MM/yyyy HH:mm}");
            sb.AppendLine();

            // رؤوس الأعمدة
            sb.AppendLine("الرقم,كود البطاقة,اسم المستخدم,كلمة المرور,القيمة,العملة,تاريخ الإنشاء");

            // البيانات
            int counter = 1;
            foreach (var card in cards)
            {
                sb.AppendLine($"{counter},{card.CardCode},{card.Username},{card.Password},{card.Value},{card.Currency},{card.CreatedDate:dd/MM/yyyy}");
                counter++;
            }

            return sb.ToString();
        }

        private string CreateSimpleFormat(CardGenerator.BatchInfo batch, List<CardGenerator.CardInfo> cards)
        {
            var sb = new StringBuilder();

            sb.AppendLine($"# بطاقات الدفعة: {batch.BatchName}");
            sb.AppendLine($"# العدد: {cards.Count} بطاقة");
            sb.AppendLine();

            foreach (var card in cards)
            {
                sb.AppendLine($"{card.CardCode} | {card.Username} | {card.Password} | {card.Value} {card.Currency}");
            }

            return sb.ToString();
        }

        private string CreateMikroTikFormat(CardGenerator.BatchInfo batch, List<CardGenerator.CardInfo> cards)
        {
            var sb = new StringBuilder();

            sb.AppendLine("# MikroTik User Manager Import Format");
            sb.AppendLine($"# Batch: {batch.BatchName}");
            sb.AppendLine($"# Generated: {DateTime.Now:dd/MM/yyyy HH:mm}");
            sb.AppendLine();

            string profile = configManager.HotspotProfile;

            foreach (var card in cards)
            {
                // تنسيق MikroTik لإضافة المستخدمين
                sb.AppendLine($"/ip hotspot user add name={card.Username} password={card.Password} profile={profile} comment=\"{card.CardCode} - {card.Value} {card.Currency}\"");
            }

            return sb.ToString();
        }

        private string CreateJSONFormat(CardGenerator.BatchInfo batch, List<CardGenerator.CardInfo> cards)
        {
            var sb = new StringBuilder();

            sb.AppendLine("{");
            sb.AppendLine($"  \"batchInfo\": {{");
            sb.AppendLine($"    \"batchId\": \"{batch.BatchId}\",");
            sb.AppendLine($"    \"batchName\": \"{batch.BatchName}\",");
            sb.AppendLine($"    \"totalCards\": {batch.TotalCards},");
            sb.AppendLine($"    \"cardValue\": {batch.CardValue},");
            sb.AppendLine($"    \"currency\": \"{batch.Currency}\",");
            sb.AppendLine($"    \"createdDate\": \"{batch.CreatedDate:yyyy-MM-ddTHH:mm:ss}\",");
            sb.AppendLine($"    \"exportDate\": \"{DateTime.Now:yyyy-MM-ddTHH:mm:ss}\"");
            sb.AppendLine($"  }},");
            sb.AppendLine($"  \"cards\": [");

            for (int i = 0; i < cards.Count; i++)
            {
                var card = cards[i];
                sb.AppendLine($"    {{");
                sb.AppendLine($"      \"cardCode\": \"{card.CardCode}\",");
                sb.AppendLine($"      \"username\": \"{card.Username}\",");
                sb.AppendLine($"      \"password\": \"{card.Password}\",");
                sb.AppendLine($"      \"value\": {card.Value},");
                sb.AppendLine($"      \"currency\": \"{card.Currency}\",");
                sb.AppendLine($"      \"createdDate\": \"{card.CreatedDate:yyyy-MM-ddTHH:mm:ss}\"");
                sb.Append($"    }}");
                
                if (i < cards.Count - 1)
                    sb.AppendLine(",");
                else
                    sb.AppendLine();
            }

            sb.AppendLine($"  ]");
            sb.AppendLine("}");

            return sb.ToString();
        }

        /// <summary>
        /// تصدير قائمة بأكواد البطاقات فقط
        /// </summary>
        public string ExportCardCodesOnly(string batchId, string outputPath = null)
        {
            try
            {
                var cardGenerator = new CardGenerator();
                var batch = cardGenerator.GetAllBatches().Find(b => b.BatchId == batchId);
                var cards = cardGenerator.GetBatchCards(batchId);

                if (batch == null || cards == null || cards.Count == 0)
                {
                    throw new Exception("لم يتم العثور على الدفعة أو البطاقات");
                }

                if (string.IsNullOrEmpty(outputPath))
                {
                    string exportDir = Path.Combine(configManager.ExportPath, "Text");
                    Directory.CreateDirectory(exportDir);
                    outputPath = Path.Combine(exportDir, $"{batch.BatchName}_codes_only_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                }

                var sb = new StringBuilder();
                sb.AppendLine($"# أكواد بطاقات الدفعة: {batch.BatchName}");
                sb.AppendLine($"# العدد: {cards.Count} بطاقة");
                sb.AppendLine($"# تاريخ التصدير: {DateTime.Now:dd/MM/yyyy HH:mm}");
                sb.AppendLine();

                foreach (var card in cards)
                {
                    sb.AppendLine(card.CardCode);
                }

                File.WriteAllText(outputPath, sb.ToString(), Encoding.UTF8);

                dbManager.LogOperation("تصدير أكواد", $"تم تصدير أكواد الدفعة {batch.BatchName}: {outputPath}");
                return outputPath;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تصدير الأكواد: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير بيانات الدخول للمستخدمين
        /// </summary>
        public string ExportUserCredentials(string batchId, string outputPath = null)
        {
            try
            {
                var cardGenerator = new CardGenerator();
                var batch = cardGenerator.GetAllBatches().Find(b => b.BatchId == batchId);
                var cards = cardGenerator.GetBatchCards(batchId);

                if (batch == null || cards == null || cards.Count == 0)
                {
                    throw new Exception("لم يتم العثور على الدفعة أو البطاقات");
                }

                if (string.IsNullOrEmpty(outputPath))
                {
                    string exportDir = Path.Combine(configManager.ExportPath, "Text");
                    Directory.CreateDirectory(exportDir);
                    outputPath = Path.Combine(exportDir, $"{batch.BatchName}_credentials_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                }

                var sb = new StringBuilder();
                sb.AppendLine($"# بيانات دخول المستخدمين - الدفعة: {batch.BatchName}");
                sb.AppendLine($"# العدد: {cards.Count} مستخدم");
                sb.AppendLine($"# تاريخ التصدير: {DateTime.Now:dd/MM/yyyy HH:mm}");
                sb.AppendLine();
                sb.AppendLine("اسم المستخدم | كلمة المرور | القيمة");
                sb.AppendLine("----------------------------------------");

                foreach (var card in cards)
                {
                    sb.AppendLine($"{card.Username} | {card.Password} | {card.Value} {card.Currency}");
                }

                File.WriteAllText(outputPath, sb.ToString(), Encoding.UTF8);

                dbManager.LogOperation("تصدير بيانات دخول", $"تم تصدير بيانات دخول الدفعة {batch.BatchName}: {outputPath}");
                return outputPath;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تصدير بيانات الدخول: {ex.Message}");
                throw;
            }
        }
    }

    /// <summary>
    /// تنسيقات التصدير النصي المتاحة
    /// </summary>
    public enum TextFormat
    {
        /// <summary>
        /// تنسيق مفصل مع جميع المعلومات
        /// </summary>
        Detailed,

        /// <summary>
        /// تنسيق CSV للاستيراد في برامج أخرى
        /// </summary>
        CSV,

        /// <summary>
        /// تنسيق بسيط مع المعلومات الأساسية
        /// </summary>
        Simple,

        /// <summary>
        /// تنسيق MikroTik لاستيراد المستخدمين
        /// </summary>
        MikroTik,

        /// <summary>
        /// تنسيق JSON للتطبيقات الويب
        /// </summary>
        JSON
    }
}
