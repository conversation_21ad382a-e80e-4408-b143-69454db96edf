<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- إعدادات التطبيق الأساسية -->
    <add key="AppName" value="AdenLink - عدن لنك" />
    <add key="AppVersion" value="1.0.0" />
    <add key="CompanyName" value="AdenLink" />
    
    <!-- إعدادات قاعدة البيانات -->
    <add key="DatabasePath" value="database\AdenLinkDB.db" />
    <add key="ArchiveDatabasePath" value="database\AdenLinkArchive.db" />
    <add key="BackupPath" value="database\backup" />
    
    <!-- إعدادات البطاقات الافتراضية -->
    <add key="DefaultCardPrefix" value="AL" />
    <add key="DefaultCurrency" value="ريال" />
    <add key="DefaultCardValue" value="10" />
    <add key="CardCodeLength" value="12" />
    <add key="UsernameLength" value="8" />
    <add key="PasswordLength" value="10" />
    
    <!-- إعدادات التصدير -->
    <add key="ExportPath" value="temp" />
    <add key="PDFTemplatePath" value="assets\templates\card_template.pdf" />
    <add key="ExcelTemplatePath" value="assets\templates\cards_template.xlsx" />
    <add key="ImageTemplatePath" value="assets\templates\card_background.jpg" />
    
    <!-- إعدادات الشبكة -->
    <add key="FTPServer" value="" />
    <add key="FTPPort" value="21" />
    <add key="FTPUsername" value="" />
    <add key="FTPPassword" value="" />
    <add key="FTPPath" value="/cards" />
    <add key="FTPEnabled" value="false" />
    
    <!-- إعدادات MikroTik -->
    <add key="MikroTikIP" value="" />
    <add key="MikroTikPort" value="8728" />
    <add key="MikroTikUsername" value="" />
    <add key="MikroTikPassword" value="" />
    <add key="MikroTikEnabled" value="false" />
    <add key="HotspotProfile" value="default" />
    
    <!-- إعدادات SSH -->
    <add key="SSHServer" value="" />
    <add key="SSHPort" value="22" />
    <add key="SSHUsername" value="" />
    <add key="SSHPassword" value="" />
    <add key="SSHEnabled" value="false" />
    <add key="SSHRemotePath" value="/var/www/html/cards" />
    
    <!-- إعدادات الأمان -->
    <add key="EnableLogging" value="true" />
    <add key="LogLevel" value="INFO" />
    <add key="MaxLogFiles" value="30" />
    <add key="EnableBackup" value="true" />
    <add key="BackupInterval" value="24" />
    <add key="MaxBackupFiles" value="7" />
    
    <!-- إعدادات الواجهة -->
    <add key="Language" value="ar" />
    <add key="Theme" value="Default" />
    <add key="FontFamily" value="Tahoma" />
    <add key="FontSize" value="12" />
    <add key="RightToLeft" value="true" />
    
    <!-- إعدادات الطباعة -->
    <add key="DefaultPrinter" value="" />
    <add key="PrintQuality" value="High" />
    <add key="PaperSize" value="A4" />
    <add key="CardsPerPage" value="4" />
    
    <!-- إعدادات التحديث -->
    <add key="CheckForUpdates" value="true" />
    <add key="UpdateServer" value="" />
    <add key="AutoUpdate" value="false" />
  </appSettings>
  
  <connectionStrings>
    <add name="DefaultConnection" 
         connectionString="Data Source=database\AdenLinkDB.db;Version=3;" 
         providerName="System.Data.SQLite" />
    <add name="ArchiveConnection" 
         connectionString="Data Source=database\AdenLinkArchive.db;Version=3;" 
         providerName="System.Data.SQLite" />
  </connectionStrings>
  
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SQLite" publicKeyToken="db937bc2d44ff139" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.0.118.0" newVersion="1.0.118.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
