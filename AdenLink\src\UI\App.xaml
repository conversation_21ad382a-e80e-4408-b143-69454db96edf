<Application x:Class="AdenLink.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- الألوان الأساسية -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2E86AB"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#A23B72"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#F18F01"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="TextBrush" Color="#333333"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#DDDDDD"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#28A745"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FFC107"/>
        <SolidColorBrush x:Key="ErrorBrush" Color="#DC3545"/>

        <!-- الخطوط -->
        <FontFamily x:Key="ArabicFont">Tahoma, Arial Unicode MS</FontFamily>
        
        <!-- أنماط الأزرار -->
        <Style x:Key="PrimaryButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1E5F7A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0E4F6A"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
            <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#822B62"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#621B52"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="AccentButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
            <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D17F01"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#B16F01"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- أنماط النصوص -->
        <Style x:Key="HeaderText" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="SubHeaderText" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <Style x:Key="BodyText" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="LabelText" TargetType="Label">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
            <Setter Property="Margin" Value="0,0,5,5"/>
        </Style>

        <!-- أنماط حقول الإدخال -->
        <Style x:Key="InputTextBox" TargetType="TextBox">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="InputComboBox" TargetType="ComboBox">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        </Style>

        <!-- أنماط البطاقات -->
        <Style x:Key="CardPanel" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط الجداول -->
        <Style x:Key="DataGridStyle" TargetType="DataGrid">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="AlternatingRowBackground" Value="#F9F9F9"/>
            <Setter Property="RowBackground" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
        </Style>

        <!-- أنماط شريط الحالة -->
        <Style x:Key="StatusBarStyle" TargetType="StatusBar">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="25"/>
        </Style>

        <!-- أنماط القوائم -->
        <Style x:Key="MenuStyle" TargetType="Menu">
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <!-- أنماط أشرطة الأدوات -->
        <Style x:Key="ToolBarStyle" TargetType="ToolBar">
            <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
        </Style>
    </Application.Resources>
</Application>
