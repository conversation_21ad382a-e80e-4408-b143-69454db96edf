using System;
using System.Collections.Generic;
using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using AdenLink.Core;
using AdenLink.Database;

namespace AdenLink.Export
{
    public class PDFExporter
    {
        private DatabaseManager dbManager;
        private ConfigManager configManager;

        public PDFExporter()
        {
            dbManager = new DatabaseManager();
            configManager = ConfigManager.Instance;
        }

        /// <summary>
        /// تصدير دفعة البطاقات إلى ملف PDF
        /// </summary>
        public string ExportBatchToPDF(string batchId, string outputPath = null)
        {
            try
            {
                var cardGenerator = new CardGenerator();
                var batch = cardGenerator.GetAllBatches().Find(b => b.BatchId == batchId);
                var cards = cardGenerator.GetBatchCards(batchId);

                if (batch == null || cards == null || cards.Count == 0)
                {
                    throw new Exception("لم يتم العثور على الدفعة أو البطاقات");
                }

                // تحديد مسار الإخراج
                if (string.IsNullOrEmpty(outputPath))
                {
                    string exportDir = Path.Combine(configManager.ExportPath, "PDF");
                    Directory.CreateDirectory(exportDir);
                    outputPath = Path.Combine(exportDir, $"{batch.BatchName}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf");
                }

                // إنشاء مستند PDF
                using (var document = new Document(PageSize.A4, 20, 20, 30, 30))
                {
                    using (var writer = PdfWriter.GetInstance(document, new FileStream(outputPath, FileMode.Create)))
                    {
                        document.Open();

                        // إضافة الخطوط العربية
                        var arabicFont = GetArabicFont();
                        var titleFont = new Font(arabicFont, 18, Font.BOLD);
                        var headerFont = new Font(arabicFont, 14, Font.BOLD);
                        var bodyFont = new Font(arabicFont, 12, Font.NORMAL);

                        // عنوان المستند
                        AddDocumentHeader(document, batch, titleFont, headerFont, bodyFont);

                        // إضافة البطاقات
                        AddCardsToDocument(document, cards, bodyFont, headerFont);

                        // إضافة تذييل
                        AddDocumentFooter(document, bodyFont);

                        document.Close();
                    }
                }

                dbManager.LogOperation("تصدير PDF", $"تم تصدير الدفعة {batch.BatchName} إلى PDF: {outputPath}");
                return outputPath;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تصدير PDF: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير بطاقات فردية إلى PDF للطباعة
        /// </summary>
        public string ExportCardsToPrintablePDF(List<CardGenerator.CardInfo> cards, string outputPath = null)
        {
            try
            {
                if (cards == null || cards.Count == 0)
                {
                    throw new Exception("لا توجد بطاقات للتصدير");
                }

                // تحديد مسار الإخراج
                if (string.IsNullOrEmpty(outputPath))
                {
                    string exportDir = Path.Combine(configManager.ExportPath, "PDF");
                    Directory.CreateDirectory(exportDir);
                    outputPath = Path.Combine(exportDir, $"Cards_Printable_{DateTime.Now:yyyyMMdd_HHmmss}.pdf");
                }

                // إنشاء مستند PDF بحجم مناسب للطباعة
                using (var document = new Document(PageSize.A4, 10, 10, 10, 10))
                {
                    using (var writer = PdfWriter.GetInstance(document, new FileStream(outputPath, FileMode.Create)))
                    {
                        document.Open();

                        var arabicFont = GetArabicFont();
                        var cardFont = new Font(arabicFont, 10, Font.NORMAL);
                        var cardBoldFont = new Font(arabicFont, 12, Font.BOLD);

                        // إضافة البطاقات بتنسيق قابل للطباعة (4 بطاقات في الصفحة)
                        AddPrintableCards(document, cards, cardFont, cardBoldFont);

                        document.Close();
                    }
                }

                dbManager.LogOperation("تصدير PDF للطباعة", $"تم تصدير {cards.Count} بطاقة للطباعة: {outputPath}");
                return outputPath;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تصدير PDF للطباعة: {ex.Message}");
                throw;
            }
        }

        private BaseFont GetArabicFont()
        {
            try
            {
                // محاولة تحميل خط عربي
                string fontPath = Path.Combine(configManager.GetSetting("FontPath", "assets/fonts"), "arial.ttf");
                
                if (File.Exists(fontPath))
                {
                    return BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                }
                else
                {
                    // استخدام خط افتراضي
                    return BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
                }
            }
            catch
            {
                // في حالة فشل تحميل الخط، استخدام الخط الافتراضي
                return BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            }
        }

        private void AddDocumentHeader(Document document, CardGenerator.BatchInfo batch, Font titleFont, Font headerFont, Font bodyFont)
        {
            // عنوان الشركة
            var companyTitle = new Paragraph(configManager.CompanyName, titleFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 10
            };
            document.Add(companyTitle);

            // عنوان المستند
            var docTitle = new Paragraph("تقرير بطاقات الشحن", headerFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 20
            };
            document.Add(docTitle);

            // معلومات الدفعة
            var batchInfo = new Paragraph($"معلومات الدفعة", headerFont)
            {
                SpacingAfter = 10
            };
            document.Add(batchInfo);

            var batchDetails = new Paragraph(
                $"اسم الدفعة: {batch.BatchName}\n" +
                $"معرف الدفعة: {batch.BatchId}\n" +
                $"عدد البطاقات: {batch.TotalCards}\n" +
                $"قيمة البطاقة: {batch.CardValue} {batch.Currency}\n" +
                $"القيمة الإجمالية: {(batch.TotalCards * batch.CardValue)} {batch.Currency}\n" +
                $"تاريخ الإنشاء: {batch.CreatedDate:dd/MM/yyyy HH:mm}",
                bodyFont)
            {
                SpacingAfter = 20
            };
            document.Add(batchDetails);

            // خط فاصل
            document.Add(new Paragraph("_".PadRight(80, '_'), bodyFont) { SpacingAfter = 10 });
        }

        private void AddCardsToDocument(Document document, List<CardGenerator.CardInfo> cards, Font bodyFont, Font headerFont)
        {
            // عنوان قائمة البطاقات
            var cardsTitle = new Paragraph("قائمة البطاقات", headerFont)
            {
                SpacingAfter = 10
            };
            document.Add(cardsTitle);

            // إنشاء جدول البطاقات
            var table = new PdfPTable(6) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 20, 15, 15, 10, 10, 15 });

            // رؤوس الجدول
            AddTableHeader(table, bodyFont);

            // إضافة البطاقات
            int counter = 1;
            foreach (var card in cards)
            {
                AddCardRow(table, card, counter++, bodyFont);
            }

            document.Add(table);
        }

        private void AddTableHeader(PdfPTable table, Font font)
        {
            string[] headers = { "كود البطاقة", "اسم المستخدم", "كلمة المرور", "القيمة", "العملة", "تاريخ الإنشاء" };

            foreach (string header in headers)
            {
                var cell = new PdfPCell(new Phrase(header, font))
                {
                    BackgroundColor = BaseColor.LIGHT_GRAY,
                    HorizontalAlignment = Element.ALIGN_CENTER,
                    Padding = 5
                };
                table.AddCell(cell);
            }
        }

        private void AddCardRow(PdfPTable table, CardGenerator.CardInfo card, int counter, Font font)
        {
            table.AddCell(new PdfPCell(new Phrase(card.CardCode, font)) { Padding = 3 });
            table.AddCell(new PdfPCell(new Phrase(card.Username, font)) { Padding = 3 });
            table.AddCell(new PdfPCell(new Phrase(card.Password, font)) { Padding = 3 });
            table.AddCell(new PdfPCell(new Phrase(card.Value.ToString("F2"), font)) { Padding = 3 });
            table.AddCell(new PdfPCell(new Phrase(card.Currency, font)) { Padding = 3 });
            table.AddCell(new PdfPCell(new Phrase(card.CreatedDate.ToString("dd/MM/yyyy"), font)) { Padding = 3 });
        }

        private void AddPrintableCards(Document document, List<CardGenerator.CardInfo> cards, Font cardFont, Font cardBoldFont)
        {
            int cardsPerPage = 4;
            int cardCount = 0;

            foreach (var card in cards)
            {
                if (cardCount > 0 && cardCount % cardsPerPage == 0)
                {
                    document.NewPage();
                }

                // إنشاء بطاقة قابلة للطباعة
                var cardTable = new PdfPTable(1) { WidthPercentage = 45 };
                
                // إطار البطاقة
                var cardCell = new PdfPCell()
                {
                    Border = Rectangle.BOX,
                    BorderWidth = 2,
                    Padding = 10,
                    MinimumHeight = 150
                };

                // محتوى البطاقة
                var cardContent = new Paragraph();
                cardContent.Add(new Chunk($"{configManager.CompanyName}\n", cardBoldFont));
                cardContent.Add(new Chunk("بطاقة شحن\n\n", cardFont));
                cardContent.Add(new Chunk($"الكود: {card.CardCode}\n", cardBoldFont));
                cardContent.Add(new Chunk($"المستخدم: {card.Username}\n", cardFont));
                cardContent.Add(new Chunk($"كلمة المرور: {card.Password}\n", cardFont));
                cardContent.Add(new Chunk($"القيمة: {card.Value} {card.Currency}\n", cardBoldFont));
                cardContent.Add(new Chunk($"تاريخ الإنشاء: {card.CreatedDate:dd/MM/yyyy}", cardFont));

                cardCell.AddElement(cardContent);
                cardTable.AddCell(cardCell);

                document.Add(cardTable);
                document.Add(new Paragraph(" ")); // مسافة بين البطاقات

                cardCount++;
            }
        }

        private void AddDocumentFooter(Document document, Font font)
        {
            document.Add(new Paragraph(" "));
            document.Add(new Paragraph("_".PadRight(80, '_'), font));
            
            var footer = new Paragraph(
                $"تم إنشاء هذا التقرير بواسطة {configManager.AppName}\n" +
                $"تاريخ الإنشاء: {DateTime.Now:dd/MM/yyyy HH:mm:ss}\n" +
                $"المستخدم: {Environment.UserName}",
                font)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingBefore = 10
            };
            document.Add(footer);
        }

        /// <summary>
        /// تصدير إحصائيات الدفعة إلى PDF
        /// </summary>
        public string ExportBatchStatsToPDF(string batchId, string outputPath = null)
        {
            try
            {
                // سيتم تنفيذ هذه الوظيفة لاحقاً
                dbManager.LogOperation("تصدير إحصائيات", $"تم تصدير إحصائيات الدفعة {batchId}");
                return outputPath;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تصدير إحصائيات PDF: {ex.Message}");
                throw;
            }
        }
    }
}
