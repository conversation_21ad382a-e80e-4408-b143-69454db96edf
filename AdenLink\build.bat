@echo off
echo ========================================
echo    بناء نظام AdenLink - عدن لنك
echo ========================================
echo.

REM التحقق من وجود MSBuild
where msbuild >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: MSBuild غير موجود في النظام
    echo يرجى تثبيت Visual Studio أو Build Tools
    pause
    exit /b 1
)

REM إنشاء المجلدات المطلوبة
echo إنشاء المجلدات المطلوبة...
if not exist "bin" mkdir bin
if not exist "bin\Debug" mkdir bin\Debug
if not exist "bin\Release" mkdir bin\Release
if not exist "database" mkdir database
if not exist "temp" mkdir temp
if not exist "temp\Excel" mkdir temp\Excel
if not exist "temp\PDF" mkdir temp\PDF
if not exist "temp\Images" mkdir temp\Images
if not exist "temp\Text" mkdir temp\Text
if not exist "temp\Scripts" mkdir temp\Scripts
if not exist "temp\FTP" mkdir temp\FTP

REM تنظيف البناء السابق
echo تنظيف البناء السابق...
msbuild AdenLink.csproj /t:Clean /p:Configuration=Release /verbosity:minimal

REM استعادة الحزم
echo استعادة حزم NuGet...
nuget restore packages.config -PackagesDirectory packages

REM بناء المشروع
echo بناء المشروع...
msbuild AdenLink.csproj /t:Build /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo خطأ: فشل في بناء المشروع
    pause
    exit /b 1
)

REM نسخ الملفات المطلوبة
echo نسخ الملفات المطلوبة...
xcopy "config\*.*" "bin\Release\config\" /E /I /Y >nul
xcopy "assets\*.*" "bin\Release\assets\" /E /I /Y >nul
xcopy "docs\*.*" "bin\Release\docs\" /E /I /Y >nul

REM إنشاء مجلدات العمل
if not exist "bin\Release\database" mkdir "bin\Release\database"
if not exist "bin\Release\temp" mkdir "bin\Release\temp"
if not exist "bin\Release\temp\Excel" mkdir "bin\Release\temp\Excel"
if not exist "bin\Release\temp\PDF" mkdir "bin\Release\temp\PDF"
if not exist "bin\Release\temp\Images" mkdir "bin\Release\temp\Images"
if not exist "bin\Release\temp\Text" mkdir "bin\Release\temp\Text"
if not exist "bin\Release\temp\Scripts" mkdir "bin\Release\temp\Scripts"
if not exist "bin\Release\temp\FTP" mkdir "bin\Release\temp\FTP"

REM إنشاء ملف معلومات البناء
echo إنشاء ملف معلومات البناء...
echo Build Information > "bin\Release\BuildInfo.txt"
echo ================== >> "bin\Release\BuildInfo.txt"
echo Build Date: %date% %time% >> "bin\Release\BuildInfo.txt"
echo Build Machine: %COMPUTERNAME% >> "bin\Release\BuildInfo.txt"
echo Build User: %USERNAME% >> "bin\Release\BuildInfo.txt"
echo Configuration: Release >> "bin\Release\BuildInfo.txt"
echo Platform: Any CPU >> "bin\Release\BuildInfo.txt"
echo .NET Framework: 4.7.2 >> "bin\Release\BuildInfo.txt"

echo.
echo ========================================
echo تم بناء المشروع بنجاح!
echo ========================================
echo.
echo الملفات متوفرة في: bin\Release\
echo الملف التنفيذي: bin\Release\AdenLink.exe
echo.

REM اختبار تشغيل سريع
echo اختبار التشغيل...
"bin\Release\AdenLink.exe" --version >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ اختبار التشغيل نجح
) else (
    echo ⚠ تحذير: قد تكون هناك مشكلة في التشغيل
)

echo.
echo هل تريد فتح مجلد الإخراج؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    explorer "bin\Release"
)

pause
