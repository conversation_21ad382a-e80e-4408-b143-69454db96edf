using System;
using System.Windows;
using System.Windows.Threading;
using AdenLink.Core;
using AdenLink.Database;

namespace AdenLink
{
    public partial class App : Application
    {
        private DatabaseManager dbManager;
        private ConfigManager configManager;

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                // تهيئة النظام
                InitializeApplication();

                // تسجيل بدء التطبيق
                dbManager.LogOperation("بدء التطبيق", "تم بدء تشغيل AdenLink بنجاح");

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                // عرض رسالة خطأ في حالة فشل التهيئة
                MessageBox.Show(
                    $"حدث خطأ أثناء بدء التطبيق:\n{ex.Message}",
                    "خطأ في بدء التطبيق",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                // إنهاء التطبيق
                Shutdown(1);
            }
        }

        private void InitializeApplication()
        {
            // تهيئة مدير قاعدة البيانات
            dbManager = new DatabaseManager();

            // تهيئة مدير التكوين
            configManager = ConfigManager.Instance;

            // التحقق من صحة المسارات
            configManager.ValidatePaths();

            // تطبيق إعدادات الواجهة
            ApplyUISettings();

            // تسجيل معالج الأخطاء العامة
            DispatcherUnhandledException += OnUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += OnDomainUnhandledException;
        }

        private void ApplyUISettings()
        {
            try
            {
                // تطبيق اتجاه النص (من اليمين لليسار)
                if (configManager.RightToLeft)
                {
                    FrameworkElement.FlowDirectionProperty.OverrideMetadata(
                        typeof(FrameworkElement),
                        new FrameworkPropertyMetadata(FlowDirection.RightToLeft));
                }

                // تطبيق الخط الافتراضي
                string fontFamily = configManager.FontFamily;
                int fontSize = configManager.FontSize;

                // يمكن إضافة المزيد من إعدادات الواجهة هنا
            }
            catch (Exception ex)
            {
                dbManager?.LogOperation("خطأ", $"فشل في تطبيق إعدادات الواجهة: {ex.Message}");
            }
        }

        private void OnUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                // تسجيل الخطأ
                dbManager?.LogOperation("خطأ غير متوقع", $"خطأ في الواجهة: {e.Exception.Message}", e.Exception.StackTrace);

                // عرض رسالة للمستخدم
                MessageBox.Show(
                    $"حدث خطأ غير متوقع:\n{e.Exception.Message}\n\nسيتم إعادة تشغيل التطبيق.",
                    "خطأ غير متوقع",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                // منع إنهاء التطبيق
                e.Handled = true;

                // إعادة تشغيل النافذة الرئيسية
                RestartMainWindow();
            }
            catch
            {
                // في حالة فشل معالجة الخطأ، السماح بإنهاء التطبيق
                e.Handled = false;
            }
        }

        private void OnDomainUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                Exception ex = e.ExceptionObject as Exception;
                dbManager?.LogOperation("خطأ نظام", $"خطأ في النظام: {ex?.Message}", ex?.StackTrace);

                MessageBox.Show(
                    $"حدث خطأ خطير في النظام:\n{ex?.Message}",
                    "خطأ خطير",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            catch
            {
                // لا يمكن فعل شيء في هذه الحالة
            }
        }

        private void RestartMainWindow()
        {
            try
            {
                // إغلاق النافذة الحالية
                MainWindow?.Close();

                // إنشاء نافذة جديدة
                MainWindow = new MainWindow();
                MainWindow.Show();
            }
            catch (Exception ex)
            {
                dbManager?.LogOperation("خطأ", $"فشل في إعادة تشغيل النافذة الرئيسية: {ex.Message}");
                Shutdown(1);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                // تسجيل إنهاء التطبيق
                dbManager?.LogOperation("إنهاء التطبيق", "تم إنهاء AdenLink بنجاح");

                // تنظيف الموارد
                CleanupResources();

                base.OnExit(e);
            }
            catch (Exception ex)
            {
                // تسجيل خطأ الإنهاء (إذا أمكن)
                try
                {
                    dbManager?.LogOperation("خطأ", $"خطأ أثناء إنهاء التطبيق: {ex.Message}");
                }
                catch
                {
                    // لا يمكن فعل شيء
                }

                base.OnExit(e);
            }
        }

        private void CleanupResources()
        {
            try
            {
                // تنظيف الملفات المؤقتة
                CleanupTempFiles();

                // إنشاء نسخة احتياطية إذا كانت مفعلة
                if (configManager?.EnableBackup == true)
                {
                    CreateBackup();
                }
            }
            catch (Exception ex)
            {
                dbManager?.LogOperation("خطأ", $"فشل في تنظيف الموارد: {ex.Message}");
            }
        }

        private void CleanupTempFiles()
        {
            try
            {
                string tempPath = configManager?.ExportPath ?? "temp";
                if (System.IO.Directory.Exists(tempPath))
                {
                    // حذف الملفات المؤقتة الأقدم من يوم واحد
                    var tempFiles = System.IO.Directory.GetFiles(tempPath, "*.*", System.IO.SearchOption.AllDirectories);
                    foreach (string file in tempFiles)
                    {
                        var fileInfo = new System.IO.FileInfo(file);
                        if (fileInfo.CreationTime < DateTime.Now.AddDays(-1))
                        {
                            try
                            {
                                fileInfo.Delete();
                            }
                            catch
                            {
                                // تجاهل أخطاء حذف الملفات المؤقتة
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                dbManager?.LogOperation("تحذير", $"فشل في تنظيف الملفات المؤقتة: {ex.Message}");
            }
        }

        private void CreateBackup()
        {
            try
            {
                // سيتم تنفيذ آلية النسخ الاحتياطي لاحقاً
                dbManager?.LogOperation("نسخ احتياطي", "تم إنشاء نسخة احتياطية تلقائية");
            }
            catch (Exception ex)
            {
                dbManager?.LogOperation("خطأ", $"فشل في إنشاء النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على مدير قاعدة البيانات
        /// </summary>
        public static DatabaseManager GetDatabaseManager()
        {
            return ((App)Current).dbManager;
        }

        /// <summary>
        /// الحصول على مدير التكوين
        /// </summary>
        public static ConfigManager GetConfigManager()
        {
            return ((App)Current).configManager;
        }
    }
}
