using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Windows;

// معلومات عامة حول التجميع يتم التحكم فيها من خلال مجموعة السمات التالية.
// قم بتغيير قيم هذه السمات لتعديل المعلومات المرتبطة بالتجميع.
[assembly: AssemblyTitle("AdenLink - عدن لنك")]
[assembly: AssemblyDescription("نظام إدارة بطاقات الشحن الذكية")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("AdenLink")]
[assembly: AssemblyProduct("AdenLink Smart Card Management System")]
[assembly: AssemblyCopyright("Copyright © AdenLink 2025")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// تعيين ComVisible إلى false يجعل الأنواع في هذا التجميع غير مرئية
// لمكونات COM. إذا كنت بحاجة للوصول إلى نوع في هذا التجميع من
// COM، قم بتعيين سمة ComVisible إلى true على ذلك النوع.
[assembly: ComVisible(false)]

// من أجل بدء بناء تطبيقات WPF القابلة للترجمة، قم بتعيين
// <UICulture>CultureYouAreCodingWith</UICulture> في ملف .csproj
// داخل <PropertyGroup>. على سبيل المثال، إذا كنت تستخدم اللغة الإنجليزية الأمريكية
// في ملفات المصدر، قم بتعيين <UICulture> إلى en-US. ثم قم بإلغاء التعليق
// على سمة NeutralResourceLanguage أدناه. قم بتحديث "en-US" في
// السطر أدناه لمطابقة إعداد UICulture في ملف المشروع.

//[assembly: NeutralResourcesLanguage("en-US", UltimateResourceFallbackLocation.Satellite)]

[assembly: ThemeInfo(
    ResourceDictionaryLocation.None, // حيث توجد قواميس الموارد الخاصة بالموضوع
                                     // (تُستخدم إذا لم يتم العثور على مورد في الصفحة،
                                     // أو قواميس موارد التطبيق)
    ResourceDictionaryLocation.SourceAssembly // حيث يوجد قاموس الموارد العام
                                              // (تُستخدم إذا لم يتم العثور على مورد في الصفحة،
                                              // التطبيق، أو أي قواميس موارد خاصة بالموضوع)
)]

// معلومات الإصدار للتجميع تتكون من القيم الأربع التالية:
//
//      الإصدار الرئيسي
//      الإصدار الثانوي
//      رقم البناء
//      المراجعة
//
// يمكنك تحديد جميع القيم أو يمكنك افتراض رقم البناء ورقم المراجعة
// باستخدام '*' كما هو موضح أدناه:
// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("1.0.0.0")]
[assembly: AssemblyFileVersion("1.0.0.0")]
[assembly: AssemblyInformationalVersion("1.0.0")]

// معلومات إضافية للتطبيق
[assembly: AssemblyMetadata("Author", "AdenLink Development Team")]
[assembly: AssemblyMetadata("Website", "https://adenlink.com")]
[assembly: AssemblyMetadata("SupportEmail", "<EMAIL>")]
[assembly: AssemblyMetadata("BuildDate", "2025-01-23")]
[assembly: AssemblyMetadata("TargetFramework", ".NET Framework 4.7.2")]
[assembly: AssemblyMetadata("Platform", "Windows")]
[assembly: AssemblyMetadata("Architecture", "AnyCPU")]
[assembly: AssemblyMetadata("Language", "Arabic")]
[assembly: AssemblyMetadata("License", "Proprietary")]
[assembly: AssemblyMetadata("Category", "Business Application")]
[assembly: AssemblyMetadata("Keywords", "Smart Cards, Charging Cards, Network Management, MikroTik")]
