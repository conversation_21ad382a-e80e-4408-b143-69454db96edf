using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Security.Cryptography;
using System.Text;
using AdenLink.Database;

namespace AdenLink.Core
{
    public class CardGenerator
    {
        private DatabaseManager dbManager;
        private Random random;

        public CardGenerator()
        {
            dbManager = new DatabaseManager();
            random = new Random();
        }

        public class CardInfo
        {
            public string CardCode { get; set; }
            public string Username { get; set; }
            public string Password { get; set; }
            public decimal Value { get; set; }
            public string Currency { get; set; }
            public string BatchId { get; set; }
            public DateTime CreatedDate { get; set; }
        }

        public class BatchInfo
        {
            public string BatchId { get; set; }
            public string BatchName { get; set; }
            public int TotalCards { get; set; }
            public decimal CardValue { get; set; }
            public string Currency { get; set; }
            public DateTime CreatedDate { get; set; }
            public List<CardInfo> Cards { get; set; }
        }

        /// <summary>
        /// إنشاء دفعة جديدة من البطاقات
        /// </summary>
        public BatchInfo GenerateCardBatch(string batchName, int cardCount, decimal cardValue, string currency = "ريال")
        {
            try
            {
                string batchId = GenerateBatchId();
                var batch = new BatchInfo
                {
                    BatchId = batchId,
                    BatchName = batchName,
                    TotalCards = cardCount,
                    CardValue = cardValue,
                    Currency = currency,
                    CreatedDate = DateTime.Now,
                    Cards = new List<CardInfo>()
                };

                // إنشاء البطاقات
                for (int i = 0; i < cardCount; i++)
                {
                    var card = GenerateCard(cardValue, currency, batchId);
                    batch.Cards.Add(card);
                }

                // حفظ الدفعة في قاعدة البيانات
                SaveBatchToDatabase(batch);

                dbManager.LogOperation("إنشاء دفعة", $"تم إنشاء دفعة جديدة: {batchName} تحتوي على {cardCount} بطاقة");

                return batch;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في إنشاء دفعة البطاقات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء بطاقة واحدة
        /// </summary>
        private CardInfo GenerateCard(decimal value, string currency, string batchId)
        {
            string prefix = dbManager.GetSetting("CardPrefix", "AL");
            
            return new CardInfo
            {
                CardCode = GenerateUniqueCardCode(prefix),
                Username = GenerateUsername(),
                Password = GeneratePassword(),
                Value = value,
                Currency = currency,
                BatchId = batchId,
                CreatedDate = DateTime.Now
            };
        }

        /// <summary>
        /// توليد كود بطاقة فريد
        /// </summary>
        private string GenerateUniqueCardCode(string prefix)
        {
            string cardCode;
            bool isUnique = false;
            int attempts = 0;
            const int maxAttempts = 100;

            do
            {
                // توليد كود من 12 رقم
                string numbers = GenerateRandomNumbers(12);
                cardCode = $"{prefix}{numbers}";

                // التحقق من عدم وجود الكود مسبقاً
                isUnique = !CardCodeExists(cardCode);
                attempts++;

                if (attempts >= maxAttempts)
                {
                    throw new Exception("فشل في توليد كود بطاقة فريد بعد عدة محاولات");
                }

            } while (!isUnique);

            return cardCode;
        }

        /// <summary>
        /// توليد اسم مستخدم فريد
        /// </summary>
        private string GenerateUsername()
        {
            // توليد اسم مستخدم من 8 أحرف وأرقام
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var result = new StringBuilder(8);

            for (int i = 0; i < 8; i++)
            {
                result.Append(chars[random.Next(chars.Length)]);
            }

            return result.ToString();
        }

        /// <summary>
        /// توليد كلمة مرور قوية
        /// </summary>
        private string GeneratePassword()
        {
            // توليد كلمة مرور من 10 أحرف تحتوي على أحرف كبيرة وصغيرة وأرقام
            const string upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string lowerCase = "abcdefghijklmnopqrstuvwxyz";
            const string numbers = "0123456789";
            const string special = "!@#$%";

            var password = new StringBuilder(10);

            // ضمان وجود نوع واحد على الأقل من كل فئة
            password.Append(upperCase[random.Next(upperCase.Length)]);
            password.Append(lowerCase[random.Next(lowerCase.Length)]);
            password.Append(numbers[random.Next(numbers.Length)]);
            password.Append(special[random.Next(special.Length)]);

            // إكمال باقي الأحرف
            string allChars = upperCase + lowerCase + numbers + special;
            for (int i = 4; i < 10; i++)
            {
                password.Append(allChars[random.Next(allChars.Length)]);
            }

            // خلط الأحرف
            return ShuffleString(password.ToString());
        }

        /// <summary>
        /// توليد أرقام عشوائية
        /// </summary>
        private string GenerateRandomNumbers(int length)
        {
            var result = new StringBuilder(length);
            for (int i = 0; i < length; i++)
            {
                result.Append(random.Next(0, 10));
            }
            return result.ToString();
        }

        /// <summary>
        /// خلط أحرف النص
        /// </summary>
        private string ShuffleString(string input)
        {
            char[] array = input.ToCharArray();
            for (int i = array.Length - 1; i > 0; i--)
            {
                int j = random.Next(i + 1);
                char temp = array[i];
                array[i] = array[j];
                array[j] = temp;
            }
            return new string(array);
        }

        /// <summary>
        /// توليد معرف دفعة فريد
        /// </summary>
        private string GenerateBatchId()
        {
            return $"BATCH_{DateTime.Now:yyyyMMdd}_{GenerateRandomNumbers(6)}";
        }

        /// <summary>
        /// التحقق من وجود كود البطاقة
        /// </summary>
        private bool CardCodeExists(string cardCode)
        {
            using (var connection = dbManager.GetConnection())
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM Cards WHERE CardCode = @cardCode";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@cardCode", cardCode);
                    int count = Convert.ToInt32(command.ExecuteScalar());
                    return count > 0;
                }
            }
        }

        /// <summary>
        /// حفظ الدفعة في قاعدة البيانات
        /// </summary>
        private void SaveBatchToDatabase(BatchInfo batch)
        {
            using (var connection = dbManager.GetConnection())
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // حفظ معلومات الدفعة
                        string insertBatch = @"
                            INSERT INTO Batches (BatchId, BatchName, TotalCards, CardValue, Currency, CreatedBy) 
                            VALUES (@batchId, @batchName, @totalCards, @cardValue, @currency, @createdBy)";

                        using (var command = new SQLiteCommand(insertBatch, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@batchId", batch.BatchId);
                            command.Parameters.AddWithValue("@batchName", batch.BatchName);
                            command.Parameters.AddWithValue("@totalCards", batch.TotalCards);
                            command.Parameters.AddWithValue("@cardValue", batch.CardValue);
                            command.Parameters.AddWithValue("@currency", batch.Currency);
                            command.Parameters.AddWithValue("@createdBy", Environment.UserName);
                            command.ExecuteNonQuery();
                        }

                        // حفظ البطاقات
                        string insertCard = @"
                            INSERT INTO Cards (CardCode, Username, Password, Value, Currency, BatchId) 
                            VALUES (@cardCode, @username, @password, @value, @currency, @batchId)";

                        foreach (var card in batch.Cards)
                        {
                            using (var command = new SQLiteCommand(insertCard, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@cardCode", card.CardCode);
                                command.Parameters.AddWithValue("@username", card.Username);
                                command.Parameters.AddWithValue("@password", card.Password);
                                command.Parameters.AddWithValue("@value", card.Value);
                                command.Parameters.AddWithValue("@currency", card.Currency);
                                command.Parameters.AddWithValue("@batchId", card.BatchId);
                                command.ExecuteNonQuery();
                            }
                        }

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// الحصول على جميع الدفعات
        /// </summary>
        public List<BatchInfo> GetAllBatches()
        {
            var batches = new List<BatchInfo>();

            using (var connection = dbManager.GetConnection())
            {
                connection.Open();
                string query = "SELECT * FROM Batches ORDER BY CreatedDate DESC";

                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        batches.Add(new BatchInfo
                        {
                            BatchId = reader["BatchId"].ToString(),
                            BatchName = reader["BatchName"].ToString(),
                            TotalCards = Convert.ToInt32(reader["TotalCards"]),
                            CardValue = Convert.ToDecimal(reader["CardValue"]),
                            Currency = reader["Currency"].ToString(),
                            CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                        });
                    }
                }
            }

            return batches;
        }

        /// <summary>
        /// الحصول على بطاقات دفعة معينة
        /// </summary>
        public List<CardInfo> GetBatchCards(string batchId)
        {
            var cards = new List<CardInfo>();

            using (var connection = dbManager.GetConnection())
            {
                connection.Open();
                string query = "SELECT * FROM Cards WHERE BatchId = @batchId ORDER BY CreatedDate";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@batchId", batchId);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            cards.Add(new CardInfo
                            {
                                CardCode = reader["CardCode"].ToString(),
                                Username = reader["Username"].ToString(),
                                Password = reader["Password"].ToString(),
                                Value = Convert.ToDecimal(reader["Value"]),
                                Currency = reader["Currency"].ToString(),
                                BatchId = reader["BatchId"].ToString(),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                            });
                        }
                    }
                }
            }

            return cards;
        }
    }
}
