using System;
using System.Configuration;
using System.IO;
using AdenLink.Database;

namespace AdenLink.Core
{
    public class ConfigManager
    {
        private DatabaseManager dbManager;
        private static ConfigManager instance;
        private static readonly object lockObject = new object();

        private ConfigManager()
        {
            dbManager = new DatabaseManager();
        }

        public static ConfigManager Instance
        {
            get
            {
                if (instance == null)
                {
                    lock (lockObject)
                    {
                        if (instance == null)
                            instance = new ConfigManager();
                    }
                }
                return instance;
            }
        }

        /// <summary>
        /// الحصول على قيمة إعداد من قاعدة البيانات أو ملف التكوين
        /// </summary>
        public string GetSetting(string key, string defaultValue = "")
        {
            try
            {
                // محاولة الحصول على القيمة من قاعدة البيانات أولاً
                string dbValue = dbManager.GetSetting(key);
                if (!string.IsNullOrEmpty(dbValue))
                    return dbValue;

                // إذا لم توجد في قاعدة البيانات، البحث في ملف التكوين
                string configValue = ConfigurationManager.AppSettings[key];
                if (!string.IsNullOrEmpty(configValue))
                {
                    // حفظ القيمة في قاعدة البيانات للمرات القادمة
                    SetSetting(key, configValue);
                    return configValue;
                }

                return defaultValue;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في قراءة الإعداد {key}: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// تعيين قيمة إعداد في قاعدة البيانات
        /// </summary>
        public void SetSetting(string key, string value)
        {
            try
            {
                dbManager.UpdateSetting(key, value);
                dbManager.LogOperation("تحديث إعداد", $"تم تحديث الإعداد {key} = {value}");
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تحديث الإعداد {key}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على قيمة رقمية
        /// </summary>
        public int GetIntSetting(string key, int defaultValue = 0)
        {
            string value = GetSetting(key, defaultValue.ToString());
            if (int.TryParse(value, out int result))
                return result;
            return defaultValue;
        }

        /// <summary>
        /// الحصول على قيمة عشرية
        /// </summary>
        public decimal GetDecimalSetting(string key, decimal defaultValue = 0)
        {
            string value = GetSetting(key, defaultValue.ToString());
            if (decimal.TryParse(value, out decimal result))
                return result;
            return defaultValue;
        }

        /// <summary>
        /// الحصول على قيمة منطقية
        /// </summary>
        public bool GetBoolSetting(string key, bool defaultValue = false)
        {
            string value = GetSetting(key, defaultValue.ToString());
            if (bool.TryParse(value, out bool result))
                return result;
            return defaultValue;
        }

        /// <summary>
        /// تعيين قيمة رقمية
        /// </summary>
        public void SetIntSetting(string key, int value)
        {
            SetSetting(key, value.ToString());
        }

        /// <summary>
        /// تعيين قيمة عشرية
        /// </summary>
        public void SetDecimalSetting(string key, decimal value)
        {
            SetSetting(key, value.ToString());
        }

        /// <summary>
        /// تعيين قيمة منطقية
        /// </summary>
        public void SetBoolSetting(string key, bool value)
        {
            SetSetting(key, value.ToString());
        }

        // خصائص سريعة للإعدادات الشائعة
        public string AppName => GetSetting("AppName", "AdenLink - عدن لنك");
        public string AppVersion => GetSetting("AppVersion", "1.0.0");
        public string CompanyName => GetSetting("CompanyName", "AdenLink");

        public string DefaultCardPrefix => GetSetting("DefaultCardPrefix", "AL");
        public string DefaultCurrency => GetSetting("DefaultCurrency", "ريال");
        public decimal DefaultCardValue => GetDecimalSetting("DefaultCardValue", 10);

        public string ExportPath => GetSetting("ExportPath", "temp");
        public string DatabasePath => GetSetting("DatabasePath", "database\\AdenLinkDB.db");

        public bool FTPEnabled => GetBoolSetting("FTPEnabled", false);
        public string FTPServer => GetSetting("FTPServer", "");
        public int FTPPort => GetIntSetting("FTPPort", 21);
        public string FTPUsername => GetSetting("FTPUsername", "");
        public string FTPPassword => GetSetting("FTPPassword", "");
        public string FTPPath => GetSetting("FTPPath", "/cards");

        public bool MikroTikEnabled => GetBoolSetting("MikroTikEnabled", false);
        public string MikroTikIP => GetSetting("MikroTikIP", "");
        public int MikroTikPort => GetIntSetting("MikroTikPort", 8728);
        public string MikroTikUsername => GetSetting("MikroTikUsername", "");
        public string MikroTikPassword => GetSetting("MikroTikPassword", "");
        public string HotspotProfile => GetSetting("HotspotProfile", "default");

        public bool SSHEnabled => GetBoolSetting("SSHEnabled", false);
        public string SSHServer => GetSetting("SSHServer", "");
        public int SSHPort => GetIntSetting("SSHPort", 22);
        public string SSHUsername => GetSetting("SSHUsername", "");
        public string SSHPassword => GetSetting("SSHPassword", "");
        public string SSHRemotePath => GetSetting("SSHRemotePath", "/var/www/html/cards");

        public bool EnableLogging => GetBoolSetting("EnableLogging", true);
        public bool EnableBackup => GetBoolSetting("EnableBackup", true);
        public int BackupInterval => GetIntSetting("BackupInterval", 24);

        public string Language => GetSetting("Language", "ar");
        public string Theme => GetSetting("Theme", "Default");
        public string FontFamily => GetSetting("FontFamily", "Tahoma");
        public int FontSize => GetIntSetting("FontSize", 12);
        public bool RightToLeft => GetBoolSetting("RightToLeft", true);

        /// <summary>
        /// التحقق من صحة المسارات
        /// </summary>
        public void ValidatePaths()
        {
            try
            {
                // التأكد من وجود مجلد قاعدة البيانات
                string dbDir = Path.GetDirectoryName(DatabasePath);
                if (!Directory.Exists(dbDir))
                    Directory.CreateDirectory(dbDir);

                // التأكد من وجود مجلد التصدير
                if (!Directory.Exists(ExportPath))
                    Directory.CreateDirectory(ExportPath);

                // التأكد من وجود مجلدات التصدير الفرعية
                string[] exportSubDirs = { "Excel", "PDF", "Images", "Text", "Scripts", "FTP" };
                foreach (string subDir in exportSubDirs)
                {
                    string fullPath = Path.Combine(ExportPath, subDir);
                    if (!Directory.Exists(fullPath))
                        Directory.CreateDirectory(fullPath);
                }

                dbManager.LogOperation("تحقق", "تم التحقق من صحة جميع المسارات");
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في التحقق من المسارات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات للقيم الافتراضية
        /// </summary>
        public void ResetToDefaults()
        {
            try
            {
                // قائمة الإعدادات الافتراضية
                var defaultSettings = new[]
                {
                    ("DefaultCardPrefix", "AL"),
                    ("DefaultCurrency", "ريال"),
                    ("DefaultCardValue", "10"),
                    ("ExportPath", "temp"),
                    ("FTPEnabled", "false"),
                    ("MikroTikEnabled", "false"),
                    ("SSHEnabled", "false"),
                    ("EnableLogging", "true"),
                    ("EnableBackup", "true"),
                    ("Language", "ar"),
                    ("Theme", "Default"),
                    ("FontFamily", "Tahoma"),
                    ("FontSize", "12"),
                    ("RightToLeft", "true")
                };

                foreach (var (key, value) in defaultSettings)
                {
                    SetSetting(key, value);
                }

                dbManager.LogOperation("إعادة تعيين", "تم إعادة تعيين جميع الإعدادات للقيم الافتراضية");
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في إعادة تعيين الإعدادات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير الإعدادات إلى ملف
        /// </summary>
        public void ExportSettings(string filePath)
        {
            try
            {
                // سيتم تنفيذ هذه الوظيفة لاحقاً
                dbManager.LogOperation("تصدير إعدادات", $"تم تصدير الإعدادات إلى {filePath}");
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تصدير الإعدادات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// استيراد الإعدادات من ملف
        /// </summary>
        public void ImportSettings(string filePath)
        {
            try
            {
                // سيتم تنفيذ هذه الوظيفة لاحقاً
                dbManager.LogOperation("استيراد إعدادات", $"تم استيراد الإعدادات من {filePath}");
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في استيراد الإعدادات: {ex.Message}");
                throw;
            }
        }
    }
}
