using System;
using System.Data;
using System.Data.SQLite;
using System.IO;

namespace AdenLink.Database
{
    public class DatabaseManager
    {
        private string connectionString;
        private string dbPath;

        public DatabaseManager()
        {
            dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database", "AdenLinkDB.db");
            connectionString = $"Data Source={dbPath};Version=3;";
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            try
            {
                // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
                string dbDirectory = Path.GetDirectoryName(dbPath);
                if (!Directory.Exists(dbDirectory))
                {
                    Directory.CreateDirectory(dbDirectory);
                }

                // إنشاء قاعدة البيانات والجداول
                using (var connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();
                    CreateTables(connection);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}");
            }
        }

        private void CreateTables(SQLiteConnection connection)
        {
            // جدول البطاقات الرئيسي
            string createCardsTable = @"
                CREATE TABLE IF NOT EXISTS Cards (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CardCode TEXT UNIQUE NOT NULL,
                    Username TEXT NOT NULL,
                    Password TEXT NOT NULL,
                    Value DECIMAL(10,2) NOT NULL,
                    Currency TEXT NOT NULL DEFAULT 'ريال',
                    Status TEXT NOT NULL DEFAULT 'غير مستخدم',
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UsedDate DATETIME NULL,
                    ExpiryDate DATETIME NULL,
                    Description TEXT,
                    BatchId TEXT
                );";

            // جدول الدفعات
            string createBatchesTable = @"
                CREATE TABLE IF NOT EXISTS Batches (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    BatchId TEXT UNIQUE NOT NULL,
                    BatchName TEXT NOT NULL,
                    TotalCards INTEGER NOT NULL,
                    CardValue DECIMAL(10,2) NOT NULL,
                    Currency TEXT NOT NULL DEFAULT 'ريال',
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy TEXT,
                    Description TEXT
                );";

            // جدول الإعدادات
            string createSettingsTable = @"
                CREATE TABLE IF NOT EXISTS Settings (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SettingKey TEXT UNIQUE NOT NULL,
                    SettingValue TEXT NOT NULL,
                    Description TEXT,
                    UpdatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                );";

            // جدول سجل العمليات
            string createLogsTable = @"
                CREATE TABLE IF NOT EXISTS Logs (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    LogType TEXT NOT NULL,
                    Message TEXT NOT NULL,
                    Details TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UserId TEXT
                );";

            // جدول أرشيف البطاقات المستخدمة
            string createArchiveTable = @"
                CREATE TABLE IF NOT EXISTS CardsArchive (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CardCode TEXT NOT NULL,
                    Username TEXT NOT NULL,
                    UsedDate DATETIME NOT NULL,
                    UserIP TEXT,
                    UserMAC TEXT,
                    ArchiveDate DATETIME DEFAULT CURRENT_TIMESTAMP
                );";

            // تنفيذ إنشاء الجداول
            using (var command = new SQLiteCommand(createCardsTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createBatchesTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createSettingsTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createLogsTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createArchiveTable, connection))
                command.ExecuteNonQuery();

            // إدراج الإعدادات الافتراضية
            InsertDefaultSettings(connection);
        }

        private void InsertDefaultSettings(SQLiteConnection connection)
        {
            var defaultSettings = new[]
            {
                ("CompanyName", "AdenLink - عدن لنك", "اسم الشركة"),
                ("DefaultCurrency", "ريال", "العملة الافتراضية"),
                ("CardPrefix", "AL", "بادئة أكواد البطاقات"),
                ("DefaultCardValue", "10", "قيمة البطاقة الافتراضية"),
                ("ExportPath", "temp", "مسار التصدير الافتراضي"),
                ("FTPServer", "", "خادم FTP"),
                ("FTPUsername", "", "اسم مستخدم FTP"),
                ("MikroTikIP", "", "عنوان IP لجهاز MikroTik"),
                ("MikroTikUsername", "", "اسم مستخدم MikroTik"),
                ("BackupEnabled", "true", "تفعيل النسخ الاحتياطي")
            };

            foreach (var (key, value, description) in defaultSettings)
            {
                string insertSetting = @"
                    INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) 
                    VALUES (@key, @value, @description)";

                using (var command = new SQLiteCommand(insertSetting, connection))
                {
                    command.Parameters.AddWithValue("@key", key);
                    command.Parameters.AddWithValue("@value", value);
                    command.Parameters.AddWithValue("@description", description);
                    command.ExecuteNonQuery();
                }
            }
        }

        public SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(connectionString);
        }

        public void LogOperation(string logType, string message, string details = null)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    string insertLog = @"
                        INSERT INTO Logs (LogType, Message, Details) 
                        VALUES (@logType, @message, @details)";

                    using (var command = new SQLiteCommand(insertLog, connection))
                    {
                        command.Parameters.AddWithValue("@logType", logType);
                        command.Parameters.AddWithValue("@message", message);
                        command.Parameters.AddWithValue("@details", details ?? "");
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل تسجيل العملية، لا نريد إيقاف التطبيق
                Console.WriteLine($"خطأ في تسجيل العملية: {ex.Message}");
            }
        }

        public string GetSetting(string key, string defaultValue = "")
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    string query = "SELECT SettingValue FROM Settings WHERE SettingKey = @key";

                    using (var command = new SQLiteCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@key", key);
                        var result = command.ExecuteScalar();
                        return result?.ToString() ?? defaultValue;
                    }
                }
            }
            catch
            {
                return defaultValue;
            }
        }

        public void UpdateSetting(string key, string value)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                string updateQuery = @"
                    UPDATE Settings 
                    SET SettingValue = @value, UpdatedDate = CURRENT_TIMESTAMP 
                    WHERE SettingKey = @key";

                using (var command = new SQLiteCommand(updateQuery, connection))
                {
                    command.Parameters.AddWithValue("@key", key);
                    command.Parameters.AddWithValue("@value", value);
                    command.ExecuteNonQuery();
                }
            }
        }
    }
}
