<Window x:Class="AdenLink.Windows.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات النظام - AdenLink"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardPanel}" Margin="10,10,10,5">
            <StackPanel>
                <TextBlock Text="إعدادات النظام" Style="{StaticResource HeaderText}"/>
                <TextBlock Text="قم بتكوين إعدادات النظام حسب احتياجاتك" Style="{StaticResource BodyText}"/>
            </StackPanel>
        </Border>

        <!-- التبويبات -->
        <TabControl Grid.Row="1" Margin="10,5">
            <!-- إعدادات عامة -->
            <TabItem Header="عام">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="15">
                        <TextBlock Text="الإعدادات العامة" Style="{StaticResource SubHeaderText}"/>
                        
                        <Label Content="اسم الشركة:" Style="{StaticResource LabelText}"/>
                        <TextBox x:Name="CompanyNameTextBox" Style="{StaticResource InputTextBox}"/>

                        <Label Content="بادئة أكواد البطاقات:" Style="{StaticResource LabelText}"/>
                        <TextBox x:Name="CardPrefixTextBox" Style="{StaticResource InputTextBox}" MaxLength="5"/>

                        <Label Content="العملة الافتراضية:" Style="{StaticResource LabelText}"/>
                        <ComboBox x:Name="DefaultCurrencyComboBox" Style="{StaticResource InputComboBox}">
                            <ComboBoxItem Content="ريال"/>
                            <ComboBoxItem Content="دولار"/>
                            <ComboBoxItem Content="يورو"/>
                            <ComboBoxItem Content="دينار"/>
                        </ComboBox>

                        <Label Content="قيمة البطاقة الافتراضية:" Style="{StaticResource LabelText}"/>
                        <TextBox x:Name="DefaultCardValueTextBox" Style="{StaticResource InputTextBox}"/>

                        <TextBlock Text="إعدادات الواجهة" Style="{StaticResource SubHeaderText}" Margin="0,20,0,10"/>
                        
                        <Label Content="الخط:" Style="{StaticResource LabelText}"/>
                        <ComboBox x:Name="FontFamilyComboBox" Style="{StaticResource InputComboBox}">
                            <ComboBoxItem Content="Tahoma"/>
                            <ComboBoxItem Content="Arial"/>
                            <ComboBoxItem Content="Segoe UI"/>
                            <ComboBoxItem Content="Calibri"/>
                        </ComboBox>

                        <Label Content="حجم الخط:" Style="{StaticResource LabelText}"/>
                        <ComboBox x:Name="FontSizeComboBox" Style="{StaticResource InputComboBox}">
                            <ComboBoxItem Content="10"/>
                            <ComboBoxItem Content="12"/>
                            <ComboBoxItem Content="14"/>
                            <ComboBoxItem Content="16"/>
                            <ComboBoxItem Content="18"/>
                        </ComboBox>

                        <CheckBox x:Name="RightToLeftCheckBox" Content="اتجاه النص من اليمين لليسار" 
                                  Margin="0,10" FontFamily="{StaticResource ArabicFont}"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات الشبكة -->
            <TabItem Header="الشبكة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="15">
                        <!-- إعدادات FTP -->
                        <TextBlock Text="إعدادات FTP" Style="{StaticResource SubHeaderText}"/>
                        
                        <CheckBox x:Name="FTPEnabledCheckBox" Content="تفعيل FTP" 
                                  FontFamily="{StaticResource ArabicFont}" Margin="0,5"/>

                        <Label Content="خادم FTP:" Style="{StaticResource LabelText}"/>
                        <TextBox x:Name="FTPServerTextBox" Style="{StaticResource InputTextBox}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <Label Content="اسم المستخدم:" Style="{StaticResource LabelText}"/>
                                <TextBox x:Name="FTPUsernameTextBox" Style="{StaticResource InputTextBox}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <Label Content="المنفذ:" Style="{StaticResource LabelText}"/>
                                <TextBox x:Name="FTPPortTextBox" Style="{StaticResource InputTextBox}" Text="21"/>
                            </StackPanel>
                        </Grid>

                        <Label Content="كلمة المرور:" Style="{StaticResource LabelText}"/>
                        <PasswordBox x:Name="FTPPasswordBox" Style="{StaticResource InputTextBox}"/>

                        <Label Content="المسار البعيد:" Style="{StaticResource LabelText}"/>
                        <TextBox x:Name="FTPPathTextBox" Style="{StaticResource InputTextBox}" Text="/cards"/>

                        <!-- إعدادات SSH -->
                        <TextBlock Text="إعدادات SSH" Style="{StaticResource SubHeaderText}" Margin="0,20,0,10"/>
                        
                        <CheckBox x:Name="SSHEnabledCheckBox" Content="تفعيل SSH" 
                                  FontFamily="{StaticResource ArabicFont}" Margin="0,5"/>

                        <Label Content="خادم SSH:" Style="{StaticResource LabelText}"/>
                        <TextBox x:Name="SSHServerTextBox" Style="{StaticResource InputTextBox}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <Label Content="اسم المستخدم:" Style="{StaticResource LabelText}"/>
                                <TextBox x:Name="SSHUsernameTextBox" Style="{StaticResource InputTextBox}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <Label Content="المنفذ:" Style="{StaticResource LabelText}"/>
                                <TextBox x:Name="SSHPortTextBox" Style="{StaticResource InputTextBox}" Text="22"/>
                            </StackPanel>
                        </Grid>

                        <Label Content="كلمة المرور:" Style="{StaticResource LabelText}"/>
                        <PasswordBox x:Name="SSHPasswordBox" Style="{StaticResource InputTextBox}"/>

                        <Label Content="المسار البعيد:" Style="{StaticResource LabelText}"/>
                        <TextBox x:Name="SSHRemotePathTextBox" Style="{StaticResource InputTextBox}" Text="/var/www/html/cards"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات MikroTik -->
            <TabItem Header="MikroTik">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="15">
                        <TextBlock Text="إعدادات MikroTik" Style="{StaticResource SubHeaderText}"/>
                        
                        <CheckBox x:Name="MikroTikEnabledCheckBox" Content="تفعيل اتصال MikroTik" 
                                  FontFamily="{StaticResource ArabicFont}" Margin="0,5"/>

                        <Label Content="عنوان IP:" Style="{StaticResource LabelText}"/>
                        <TextBox x:Name="MikroTikIPTextBox" Style="{StaticResource InputTextBox}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <Label Content="اسم المستخدم:" Style="{StaticResource LabelText}"/>
                                <TextBox x:Name="MikroTikUsernameTextBox" Style="{StaticResource InputTextBox}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <Label Content="المنفذ:" Style="{StaticResource LabelText}"/>
                                <TextBox x:Name="MikroTikPortTextBox" Style="{StaticResource InputTextBox}" Text="8728"/>
                            </StackPanel>
                        </Grid>

                        <Label Content="كلمة المرور:" Style="{StaticResource LabelText}"/>
                        <PasswordBox x:Name="MikroTikPasswordBox" Style="{StaticResource InputTextBox}"/>

                        <Label Content="ملف تعريف Hotspot:" Style="{StaticResource LabelText}"/>
                        <TextBox x:Name="HotspotProfileTextBox" Style="{StaticResource InputTextBox}" Text="default"/>

                        <Button x:Name="TestMikroTikButton" Content="اختبار الاتصال" 
                                Style="{StaticResource SecondaryButton}" 
                                Click="TestMikroTikButton_Click" Margin="0,10,0,0"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات النسخ الاحتياطي -->
            <TabItem Header="النسخ الاحتياطي">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="15">
                        <TextBlock Text="إعدادات النسخ الاحتياطي" Style="{StaticResource SubHeaderText}"/>
                        
                        <CheckBox x:Name="BackupEnabledCheckBox" Content="تفعيل النسخ الاحتياطي التلقائي" 
                                  FontFamily="{StaticResource ArabicFont}" Margin="0,5"/>

                        <Label Content="فترة النسخ الاحتياطي (بالساعات):" Style="{StaticResource LabelText}"/>
                        <ComboBox x:Name="BackupIntervalComboBox" Style="{StaticResource InputComboBox}">
                            <ComboBoxItem Content="6"/>
                            <ComboBoxItem Content="12"/>
                            <ComboBoxItem Content="24"/>
                            <ComboBoxItem Content="48"/>
                            <ComboBoxItem Content="168"/>
                        </ComboBox>

                        <Label Content="عدد النسخ الاحتياطية المحفوظة:" Style="{StaticResource LabelText}"/>
                        <ComboBox x:Name="MaxBackupFilesComboBox" Style="{StaticResource InputComboBox}">
                            <ComboBoxItem Content="3"/>
                            <ComboBoxItem Content="5"/>
                            <ComboBoxItem Content="7"/>
                            <ComboBoxItem Content="10"/>
                            <ComboBoxItem Content="15"/>
                        </ComboBox>

                        <TextBlock Text="إعدادات السجلات" Style="{StaticResource SubHeaderText}" Margin="0,20,0,10"/>
                        
                        <CheckBox x:Name="LoggingEnabledCheckBox" Content="تفعيل تسجيل العمليات" 
                                  FontFamily="{StaticResource ArabicFont}" Margin="0,5"/>

                        <Label Content="مستوى التسجيل:" Style="{StaticResource LabelText}"/>
                        <ComboBox x:Name="LogLevelComboBox" Style="{StaticResource InputComboBox}">
                            <ComboBoxItem Content="ERROR"/>
                            <ComboBoxItem Content="WARNING"/>
                            <ComboBoxItem Content="INFO"/>
                            <ComboBoxItem Content="DEBUG"/>
                        </ComboBox>

                        <Label Content="عدد ملفات السجل المحفوظة:" Style="{StaticResource LabelText}"/>
                        <ComboBox x:Name="MaxLogFilesComboBox" Style="{StaticResource InputComboBox}">
                            <ComboBoxItem Content="10"/>
                            <ComboBoxItem Content="20"/>
                            <ComboBoxItem Content="30"/>
                            <ComboBoxItem Content="50"/>
                        </ComboBox>

                        <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                            <Button x:Name="CreateBackupButton" Content="إنشاء نسخة احتياطية الآن" 
                                    Style="{StaticResource PrimaryButton}" 
                                    Click="CreateBackupButton_Click" Margin="0,0,10,0"/>
                            
                            <Button x:Name="ViewLogsButton" Content="عرض السجلات" 
                                    Style="{StaticResource SecondaryButton}" 
                                    Click="ViewLogsButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- أزرار التحكم -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceBrush}" 
                BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="10">
                <Button x:Name="SaveButton" Content="حفظ" Style="{StaticResource PrimaryButton}" 
                        Click="SaveButton_Click" MinWidth="100"/>
                
                <Button x:Name="ResetButton" Content="إعادة تعيين" Style="{StaticResource SecondaryButton}" 
                        Click="ResetButton_Click" MinWidth="120"/>
                
                <Button x:Name="CancelButton" Content="إلغاء" Style="{StaticResource AccentButton}" 
                        Click="CancelButton_Click" MinWidth="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
