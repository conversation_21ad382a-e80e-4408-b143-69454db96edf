using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using AdenLink.Core;
using AdenLink.Database;

namespace AdenLink
{
    public partial class MainWindow : Window
    {
        private DatabaseManager dbManager;
        private ConfigManager configManager;
        private CardGenerator cardGenerator;
        private DispatcherTimer timer;
        private List<CardGenerator.BatchInfo> allBatches;
        private string selectedBatchId;

        public MainWindow()
        {
            InitializeComponent();
            InitializeApplication();
            LoadData();
            StartTimer();
        }

        private void InitializeApplication()
        {
            try
            {
                // الحصول على المدراء من التطبيق الرئيسي
                dbManager = App.GetDatabaseManager();
                configManager = App.GetConfigManager();
                cardGenerator = new CardGenerator();

                // تطبيق إعدادات الواجهة
                ApplyUISettings();

                StatusText.Text = "تم تحميل النظام بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة الرئيسية: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyUISettings()
        {
            try
            {
                // تطبيق الخط والحجم
                FontFamily = new System.Windows.Media.FontFamily(configManager.FontFamily);
                FontSize = configManager.FontSize;

                // تطبيق اتجاه النص
                FlowDirection = configManager.RightToLeft ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تطبيق إعدادات الواجهة: {ex.Message}");
            }
        }

        private void LoadData()
        {
            try
            {
                // تحميل الدفعات
                LoadBatches();

                // تحديث الإحصائيات
                UpdateStatistics();

                // تحديث حالة الاتصال
                UpdateConnectionStatus();

                StatusText.Text = "تم تحميل البيانات بنجاح";
            }
            catch (Exception ex)
            {
                StatusText.Text = "خطأ في تحميل البيانات";
                dbManager.LogOperation("خطأ", $"فشل في تحميل البيانات: {ex.Message}");
            }
        }

        private void LoadBatches()
        {
            try
            {
                allBatches = cardGenerator.GetAllBatches();
                BatchesDataGrid.ItemsSource = allBatches;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الدفعات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            try
            {
                using (var connection = dbManager.GetConnection())
                {
                    connection.Open();

                    // إجمالي البطاقات
                    var totalCardsCmd = new System.Data.SQLite.SQLiteCommand(
                        "SELECT COUNT(*) FROM Cards", connection);
                    int totalCards = Convert.ToInt32(totalCardsCmd.ExecuteScalar());
                    TotalCardsText.Text = totalCards.ToString();

                    // البطاقات المستخدمة
                    var usedCardsCmd = new System.Data.SQLite.SQLiteCommand(
                        "SELECT COUNT(*) FROM Cards WHERE Status = 'مستخدم'", connection);
                    int usedCards = Convert.ToInt32(usedCardsCmd.ExecuteScalar());
                    UsedCardsText.Text = usedCards.ToString();

                    // البطاقات المتاحة
                    int availableCards = totalCards - usedCards;
                    AvailableCardsText.Text = availableCards.ToString();
                }
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تحديث الإحصائيات: {ex.Message}");
            }
        }

        private void UpdateConnectionStatus()
        {
            try
            {
                // تحديث حالة MikroTik
                MikroTikStatus.Fill = configManager.MikroTikEnabled ? 
                    System.Windows.Media.Brushes.Green : System.Windows.Media.Brushes.Red;

                // تحديث حالة FTP
                FTPStatus.Fill = configManager.FTPEnabled ? 
                    System.Windows.Media.Brushes.Green : System.Windows.Media.Brushes.Red;

                // تحديث حالة SSH
                SSHStatus.Fill = configManager.SSHEnabled ? 
                    System.Windows.Media.Brushes.Green : System.Windows.Media.Brushes.Red;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تحديث حالة الاتصال: {ex.Message}");
            }
        }

        private void StartTimer()
        {
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += Timer_Tick;
            timer.Start();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            try
            {
                CurrentTimeText.Text = DateTime.Now.ToString("HH:mm:ss");
            }
            catch
            {
                // تجاهل أخطاء التوقيت
            }
        }

        // معالجات الأحداث للقوائم والأزرار
        private void NewBatch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة إنشاء دفعة جديدة
                var batchWindow = new Windows.BatchWindow();
                if (batchWindow.ShowDialog() == true)
                {
                    LoadBatches();
                    UpdateStatistics();
                    StatusText.Text = "تم إنشاء دفعة جديدة بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء دفعة جديدة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenBatch_Click(object sender, RoutedEventArgs e)
        {
            // سيتم تنفيذ هذه الوظيفة لاحقاً
            StatusText.Text = "فتح دفعة - قيد التطوير";
        }

        private void ViewBatches_Click(object sender, RoutedEventArgs e)
        {
            MainTabControl.SelectedItem = BatchesTab;
            LoadBatches();
        }

        private void SearchCard_Click(object sender, RoutedEventArgs e)
        {
            // سيتم تنفيذ نافذة البحث لاحقاً
            StatusText.Text = "البحث عن بطاقة - قيد التطوير";
        }

        private void ViewArchive_Click(object sender, RoutedEventArgs e)
        {
            // سيتم تنفيذ نافذة الأرشيف لاحقاً
            StatusText.Text = "عرض الأرشيف - قيد التطوير";
        }

        private void ExportPDF_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(selectedBatchId))
                {
                    MessageBox.Show("يرجى اختيار دفعة للتصدير", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // سيتم تنفيذ التصدير لاحقاً
                StatusText.Text = "تصدير PDF - قيد التطوير";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(selectedBatchId))
                {
                    MessageBox.Show("يرجى اختيار دفعة للتصدير", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // سيتم تنفيذ التصدير لاحقاً
                StatusText.Text = "تصدير Excel - قيد التطوير";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportImages_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "تصدير الصور - قيد التطوير";
        }

        private void ExportText_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "تصدير النص - قيد التطوير";
        }

        private void ConnectMikroTik_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "اتصال MikroTik - قيد التطوير";
        }

        private void UploadFTP_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "رفع FTP - قيد التطوير";
        }

        private void UploadSSH_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "رفع SSH - قيد التطوير";
        }

        private void TestConnection_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "اختبار الاتصال - قيد التطوير";
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsWindow = new Windows.SettingsWindow();
                if (settingsWindow.ShowDialog() == true)
                {
                    ApplyUISettings();
                    UpdateConnectionStatus();
                    StatusText.Text = "تم تحديث الإعدادات";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Backup_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "النسخ الاحتياطي - قيد التطوير";
        }

        private void Restore_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "الاستعادة - قيد التطوير";
        }

        private void CleanTemp_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "تنظيف الملفات المؤقتة - قيد التطوير";
        }

        private void ViewLogs_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "عرض السجلات - قيد التطوير";
        }

        private void UserGuide_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "دليل المستخدم - قيد التطوير";
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show(
                $"{configManager.AppName}\nالإصدار: {configManager.AppVersion}\n\nنظام إدارة بطاقات الشحن الذكية\nمطور خصيصاً لـ {configManager.CompanyName}",
                "حول البرنامج",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void Search_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string searchText = SearchTextBox.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    BatchesDataGrid.ItemsSource = allBatches;
                    return;
                }

                var filteredBatches = allBatches.Where(b => 
                    b.BatchName.Contains(searchText) || 
                    b.BatchId.Contains(searchText)).ToList();

                BatchesDataGrid.ItemsSource = filteredBatches;
                StatusText.Text = $"تم العثور على {filteredBatches.Count} دفعة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearSearch_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            BatchesDataGrid.ItemsSource = allBatches;
            StatusText.Text = "تم مسح البحث";
        }

        private void ViewBatch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                selectedBatchId = button?.Tag?.ToString();

                if (!string.IsNullOrEmpty(selectedBatchId))
                {
                    var cards = cardGenerator.GetBatchCards(selectedBatchId);
                    CardsDataGrid.ItemsSource = cards;

                    var batch = allBatches.FirstOrDefault(b => b.BatchId == selectedBatchId);
                    if (batch != null)
                    {
                        SelectedBatchInfo.Text = $"دفعة: {batch.BatchName} | عدد البطاقات: {batch.TotalCards}";
                    }

                    MainTabControl.SelectedItem = CardsTab;
                    StatusText.Text = $"تم تحميل بطاقات الدفعة {selectedBatchId}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الدفعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportBatch_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            selectedBatchId = button?.Tag?.ToString();
            
            if (!string.IsNullOrEmpty(selectedBatchId))
            {
                // عرض قائمة خيارات التصدير
                var contextMenu = new ContextMenu();
                
                var pdfItem = new MenuItem { Header = "تصدير PDF" };
                pdfItem.Click += (s, args) => ExportPDF_Click(s, args);
                contextMenu.Items.Add(pdfItem);
                
                var excelItem = new MenuItem { Header = "تصدير Excel" };
                excelItem.Click += (s, args) => ExportExcel_Click(s, args);
                contextMenu.Items.Add(excelItem);
                
                contextMenu.IsOpen = true;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                timer?.Stop();
                dbManager?.LogOperation("إغلاق النافذة", "تم إغلاق النافذة الرئيسية");
            }
            catch
            {
                // تجاهل أخطاء الإغلاق
            }

            base.OnClosed(e);
        }
    }
}
