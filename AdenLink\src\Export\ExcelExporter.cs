using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using AdenLink.Core;
using AdenLink.Database;

namespace AdenLink.Export
{
    public class ExcelExporter
    {
        private DatabaseManager dbManager;
        private ConfigManager configManager;

        public ExcelExporter()
        {
            dbManager = new DatabaseManager();
            configManager = ConfigManager.Instance;
            
            // تعيين سياق الترخيص لـ EPPlus
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// تصدير دفعة البطاقات إلى ملف Excel
        /// </summary>
        public string ExportBatchToExcel(string batchId, string outputPath = null)
        {
            try
            {
                var cardGenerator = new CardGenerator();
                var batch = cardGenerator.GetAllBatches().Find(b => b.BatchId == batchId);
                var cards = cardGenerator.GetBatchCards(batchId);

                if (batch == null || cards == null || cards.Count == 0)
                {
                    throw new Exception("لم يتم العثور على الدفعة أو البطاقات");
                }

                // تحديد مسار الإخراج
                if (string.IsNullOrEmpty(outputPath))
                {
                    string exportDir = Path.Combine(configManager.ExportPath, "Excel");
                    Directory.CreateDirectory(exportDir);
                    outputPath = Path.Combine(exportDir, $"{batch.BatchName}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
                }

                using (var package = new ExcelPackage())
                {
                    // إنشاء ورقة معلومات الدفعة
                    CreateBatchInfoSheet(package, batch);

                    // إنشاء ورقة البطاقات
                    CreateCardsSheet(package, cards, batch);

                    // إنشاء ورقة الإحصائيات
                    CreateStatisticsSheet(package, batch, cards);

                    // حفظ الملف
                    package.SaveAs(new FileInfo(outputPath));
                }

                dbManager.LogOperation("تصدير Excel", $"تم تصدير الدفعة {batch.BatchName} إلى Excel: {outputPath}");
                return outputPath;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تصدير Excel: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير جميع الدفعات إلى ملف Excel واحد
        /// </summary>
        public string ExportAllBatchesToExcel(string outputPath = null)
        {
            try
            {
                var cardGenerator = new CardGenerator();
                var allBatches = cardGenerator.GetAllBatches();

                if (allBatches == null || allBatches.Count == 0)
                {
                    throw new Exception("لا توجد دفعات للتصدير");
                }

                // تحديد مسار الإخراج
                if (string.IsNullOrEmpty(outputPath))
                {
                    string exportDir = Path.Combine(configManager.ExportPath, "Excel");
                    Directory.CreateDirectory(exportDir);
                    outputPath = Path.Combine(exportDir, $"All_Batches_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
                }

                using (var package = new ExcelPackage())
                {
                    // إنشاء ورقة ملخص الدفعات
                    CreateBatchesSummarySheet(package, allBatches);

                    // إنشاء ورقة لكل دفعة
                    foreach (var batch in allBatches.Take(10)) // تحديد العدد لتجنب الملفات الكبيرة
                    {
                        var cards = cardGenerator.GetBatchCards(batch.BatchId);
                        CreateCardsSheet(package, cards, batch, batch.BatchName);
                    }

                    // إنشاء ورقة الإحصائيات العامة
                    CreateOverallStatisticsSheet(package, allBatches);

                    // حفظ الملف
                    package.SaveAs(new FileInfo(outputPath));
                }

                dbManager.LogOperation("تصدير Excel شامل", $"تم تصدير جميع الدفعات إلى Excel: {outputPath}");
                return outputPath;
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تصدير Excel الشامل: {ex.Message}");
                throw;
            }
        }

        private void CreateBatchInfoSheet(ExcelPackage package, CardGenerator.BatchInfo batch)
        {
            var worksheet = package.Workbook.Worksheets.Add("معلومات الدفعة");

            // تنسيق الورقة
            worksheet.View.RightToLeft = true;
            worksheet.Cells.Style.Font.Name = "Tahoma";
            worksheet.Cells.Style.Font.Size = 12;

            // العنوان
            worksheet.Cells["A1"].Value = configManager.CompanyName;
            worksheet.Cells["A1"].Style.Font.Size = 18;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            worksheet.Cells["A1:D1"].Merge = true;

            worksheet.Cells["A2"].Value = "تقرير معلومات الدفعة";
            worksheet.Cells["A2"].Style.Font.Size = 14;
            worksheet.Cells["A2"].Style.Font.Bold = true;
            worksheet.Cells["A2"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            worksheet.Cells["A2:D2"].Merge = true;

            // معلومات الدفعة
            int row = 4;
            AddInfoRow(worksheet, ref row, "اسم الدفعة:", batch.BatchName);
            AddInfoRow(worksheet, ref row, "معرف الدفعة:", batch.BatchId);
            AddInfoRow(worksheet, ref row, "عدد البطاقات:", batch.TotalCards.ToString());
            AddInfoRow(worksheet, ref row, "قيمة البطاقة:", $"{batch.CardValue} {batch.Currency}");
            AddInfoRow(worksheet, ref row, "القيمة الإجمالية:", $"{(batch.TotalCards * batch.CardValue)} {batch.Currency}");
            AddInfoRow(worksheet, ref row, "تاريخ الإنشاء:", batch.CreatedDate.ToString("dd/MM/yyyy HH:mm"));
            AddInfoRow(worksheet, ref row, "تاريخ التقرير:", DateTime.Now.ToString("dd/MM/yyyy HH:mm"));

            // تنسيق الأعمدة
            worksheet.Column(1).Width = 20;
            worksheet.Column(2).Width = 30;
        }

        private void CreateCardsSheet(ExcelPackage package, List<CardGenerator.CardInfo> cards, CardGenerator.BatchInfo batch, string sheetName = "البطاقات")
        {
            var worksheet = package.Workbook.Worksheets.Add(sheetName);

            // تنسيق الورقة
            worksheet.View.RightToLeft = true;
            worksheet.Cells.Style.Font.Name = "Tahoma";
            worksheet.Cells.Style.Font.Size = 11;

            // العنوان
            worksheet.Cells["A1"].Value = $"بطاقات الدفعة: {batch.BatchName}";
            worksheet.Cells["A1"].Style.Font.Size = 14;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1:F1"].Merge = true;

            // رؤوس الأعمدة
            int headerRow = 3;
            worksheet.Cells[headerRow, 1].Value = "الرقم";
            worksheet.Cells[headerRow, 2].Value = "كود البطاقة";
            worksheet.Cells[headerRow, 3].Value = "اسم المستخدم";
            worksheet.Cells[headerRow, 4].Value = "كلمة المرور";
            worksheet.Cells[headerRow, 5].Value = "القيمة";
            worksheet.Cells[headerRow, 6].Value = "تاريخ الإنشاء";

            // تنسيق رؤوس الأعمدة
            var headerRange = worksheet.Cells[headerRow, 1, headerRow, 6];
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.PatternType = ExcelFillPatternType.Solid;
            headerRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            headerRange.Style.Border.BorderAround(ExcelBorderStyle.Thick);

            // إضافة البيانات
            int dataRow = headerRow + 1;
            for (int i = 0; i < cards.Count; i++)
            {
                var card = cards[i];
                worksheet.Cells[dataRow + i, 1].Value = i + 1;
                worksheet.Cells[dataRow + i, 2].Value = card.CardCode;
                worksheet.Cells[dataRow + i, 3].Value = card.Username;
                worksheet.Cells[dataRow + i, 4].Value = card.Password;
                worksheet.Cells[dataRow + i, 5].Value = card.Value;
                worksheet.Cells[dataRow + i, 6].Value = card.CreatedDate.ToString("dd/MM/yyyy");

                // تنسيق الصفوف المتناوبة
                if (i % 2 == 1)
                {
                    var rowRange = worksheet.Cells[dataRow + i, 1, dataRow + i, 6];
                    rowRange.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                    rowRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.AliceBlue);
                }
            }

            // تنسيق الأعمدة
            worksheet.Column(1).Width = 8;   // الرقم
            worksheet.Column(2).Width = 20;  // كود البطاقة
            worksheet.Column(3).Width = 15;  // اسم المستخدم
            worksheet.Column(4).Width = 15;  // كلمة المرور
            worksheet.Column(5).Width = 10;  // القيمة
            worksheet.Column(6).Width = 15;  // تاريخ الإنشاء

            // إضافة حدود للجدول
            var tableRange = worksheet.Cells[headerRow, 1, dataRow + cards.Count - 1, 6];
            tableRange.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            tableRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            tableRange.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            tableRange.Style.Border.Right.Style = ExcelBorderStyle.Thin;
        }

        private void CreateStatisticsSheet(ExcelPackage package, CardGenerator.BatchInfo batch, List<CardGenerator.CardInfo> cards)
        {
            var worksheet = package.Workbook.Worksheets.Add("الإحصائيات");

            // تنسيق الورقة
            worksheet.View.RightToLeft = true;
            worksheet.Cells.Style.Font.Name = "Tahoma";
            worksheet.Cells.Style.Font.Size = 12;

            // العنوان
            worksheet.Cells["A1"].Value = "إحصائيات الدفعة";
            worksheet.Cells["A1"].Style.Font.Size = 16;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1:C1"].Merge = true;

            // الإحصائيات
            int row = 3;
            AddStatRow(worksheet, ref row, "إجمالي البطاقات:", cards.Count.ToString());
            AddStatRow(worksheet, ref row, "البطاقات المستخدمة:", "0"); // سيتم تحديثها لاحقاً
            AddStatRow(worksheet, ref row, "البطاقات المتاحة:", cards.Count.ToString());
            AddStatRow(worksheet, ref row, "القيمة الإجمالية:", $"{(cards.Count * batch.CardValue):N2} {batch.Currency}");
            AddStatRow(worksheet, ref row, "متوسط قيمة البطاقة:", $"{batch.CardValue:N2} {batch.Currency}");

            // إحصائيات إضافية
            row++;
            worksheet.Cells[row, 1].Value = "إحصائيات إضافية:";
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            row++;

            var creationDates = cards.GroupBy(c => c.CreatedDate.Date).ToList();
            AddStatRow(worksheet, ref row, "عدد أيام الإنشاء:", creationDates.Count.ToString());
            
            if (creationDates.Any())
            {
                var firstDate = creationDates.Min(g => g.Key);
                var lastDate = creationDates.Max(g => g.Key);
                AddStatRow(worksheet, ref row, "أول تاريخ إنشاء:", firstDate.ToString("dd/MM/yyyy"));
                AddStatRow(worksheet, ref row, "آخر تاريخ إنشاء:", lastDate.ToString("dd/MM/yyyy"));
            }

            // تنسيق الأعمدة
            worksheet.Column(1).Width = 25;
            worksheet.Column(2).Width = 20;
        }

        private void CreateBatchesSummarySheet(ExcelPackage package, List<CardGenerator.BatchInfo> batches)
        {
            var worksheet = package.Workbook.Worksheets.Add("ملخص الدفعات");

            // تنسيق الورقة
            worksheet.View.RightToLeft = true;
            worksheet.Cells.Style.Font.Name = "Tahoma";
            worksheet.Cells.Style.Font.Size = 11;

            // العنوان
            worksheet.Cells["A1"].Value = "ملخص جميع الدفعات";
            worksheet.Cells["A1"].Style.Font.Size = 14;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1:F1"].Merge = true;

            // رؤوس الأعمدة
            int headerRow = 3;
            worksheet.Cells[headerRow, 1].Value = "الرقم";
            worksheet.Cells[headerRow, 2].Value = "اسم الدفعة";
            worksheet.Cells[headerRow, 3].Value = "عدد البطاقات";
            worksheet.Cells[headerRow, 4].Value = "قيمة البطاقة";
            worksheet.Cells[headerRow, 5].Value = "القيمة الإجمالية";
            worksheet.Cells[headerRow, 6].Value = "تاريخ الإنشاء";

            // تنسيق رؤوس الأعمدة
            var headerRange = worksheet.Cells[headerRow, 1, headerRow, 6];
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.PatternType = ExcelFillPatternType.Solid;
            headerRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

            // إضافة البيانات
            int dataRow = headerRow + 1;
            for (int i = 0; i < batches.Count; i++)
            {
                var batch = batches[i];
                worksheet.Cells[dataRow + i, 1].Value = i + 1;
                worksheet.Cells[dataRow + i, 2].Value = batch.BatchName;
                worksheet.Cells[dataRow + i, 3].Value = batch.TotalCards;
                worksheet.Cells[dataRow + i, 4].Value = $"{batch.CardValue} {batch.Currency}";
                worksheet.Cells[dataRow + i, 5].Value = $"{(batch.TotalCards * batch.CardValue)} {batch.Currency}";
                worksheet.Cells[dataRow + i, 6].Value = batch.CreatedDate.ToString("dd/MM/yyyy");
            }

            // تنسيق الأعمدة
            worksheet.Column(1).Width = 8;
            worksheet.Column(2).Width = 25;
            worksheet.Column(3).Width = 12;
            worksheet.Column(4).Width = 15;
            worksheet.Column(5).Width = 15;
            worksheet.Column(6).Width = 15;
        }

        private void CreateOverallStatisticsSheet(ExcelPackage package, List<CardGenerator.BatchInfo> batches)
        {
            var worksheet = package.Workbook.Worksheets.Add("الإحصائيات العامة");

            // تنسيق الورقة
            worksheet.View.RightToLeft = true;
            worksheet.Cells.Style.Font.Name = "Tahoma";
            worksheet.Cells.Style.Font.Size = 12;

            // العنوان
            worksheet.Cells["A1"].Value = "الإحصائيات العامة للنظام";
            worksheet.Cells["A1"].Style.Font.Size = 16;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1:C1"].Merge = true;

            // الإحصائيات العامة
            int row = 3;
            int totalCards = batches.Sum(b => b.TotalCards);
            decimal totalValue = batches.Sum(b => b.TotalCards * b.CardValue);

            AddStatRow(worksheet, ref row, "إجمالي الدفعات:", batches.Count.ToString());
            AddStatRow(worksheet, ref row, "إجمالي البطاقات:", totalCards.ToString("N0"));
            AddStatRow(worksheet, ref row, "إجمالي القيمة:", totalValue.ToString("N2"));
            AddStatRow(worksheet, ref row, "متوسط البطاقات لكل دفعة:", (totalCards / (double)batches.Count).ToString("F1"));
            AddStatRow(worksheet, ref row, "متوسط قيمة الدفعة:", (totalValue / batches.Count).ToString("N2"));

            if (batches.Any())
            {
                AddStatRow(worksheet, ref row, "أقدم دفعة:", batches.Min(b => b.CreatedDate).ToString("dd/MM/yyyy"));
                AddStatRow(worksheet, ref row, "أحدث دفعة:", batches.Max(b => b.CreatedDate).ToString("dd/MM/yyyy"));
            }

            // تنسيق الأعمدة
            worksheet.Column(1).Width = 25;
            worksheet.Column(2).Width = 20;
        }

        private void AddInfoRow(ExcelWorksheet worksheet, ref int row, string label, string value)
        {
            worksheet.Cells[row, 1].Value = label;
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 2].Value = value;
            row++;
        }

        private void AddStatRow(ExcelWorksheet worksheet, ref int row, string label, string value)
        {
            worksheet.Cells[row, 1].Value = label;
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 2].Value = value;
            row++;
        }
    }
}
