<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>AdenLink</RootNamespace>
    <AssemblyName>AdenLink</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <ApplicationIcon>assets\images\adenlink.ico</ApplicationIcon>
    <StartupObject>AdenLink.App</StartupObject>
  </PropertyGroup>
  
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System.Configuration" />
  </ItemGroup>
  
  <!-- مراجع المكتبات الخارجية -->
  <ItemGroup>
    <Reference Include="System.Data.SQLite">
      <HintPath>packages\System.Data.SQLite.Core.1.0.118.0\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="iTextSharp">
      <HintPath>packages\iTextSharp.5.5.13.3\lib\iTextSharp.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus">
      <HintPath>packages\EPPlus.6.2.10\lib\net462\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="Renci.SshNet">
      <HintPath>packages\SSH.NET.2020.0.2\lib\net462\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="tik4net">
      <HintPath>packages\tik4net.3.7.1\lib\net462\tik4net.dll</HintPath>
    </Reference>
  </ItemGroup>
  
  <ItemGroup>
    <ApplicationDefinition Include="src\UI\App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Page Include="src\UI\MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="src\UI\App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="src\UI\MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  
  <ItemGroup>
    <Compile Include="src\Database\DatabaseManager.cs" />
    <Compile Include="src\Core\CardGenerator.cs" />
    <Compile Include="src\Core\ConfigManager.cs" />
    <Compile Include="src\Export\PDFExporter.cs" />
    <Compile Include="src\Export\ExcelExporter.cs" />
    <Compile Include="src\Export\ImageExporter.cs" />
    <Compile Include="src\Export\TextExporter.cs" />
    <Compile Include="src\Network\FTPManager.cs" />
    <Compile Include="src\Network\SSHManager.cs" />
    <Compile Include="src\Network\MikroTikManager.cs" />
    <Compile Include="src\UI\Windows\SettingsWindow.xaml.cs">
      <DependentUpon>SettingsWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="src\UI\Windows\BatchWindow.xaml.cs">
      <DependentUpon>BatchWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="src\UI\Controls\CardPreview.xaml.cs">
      <DependentUpon>CardPreview.xaml</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="config\app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  
  <ItemGroup>
    <Page Include="src\UI\Windows\SettingsWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="src\UI\Windows\BatchWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="src\UI\Controls\CardPreview.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  
  <ItemGroup>
    <Folder Include="assets\fonts\" />
    <Folder Include="assets\images\" />
    <Folder Include="assets\templates\" />
    <Folder Include="database\" />
    <Folder Include="temp\Excel\" />
    <Folder Include="temp\PDF\" />
    <Folder Include="temp\Images\" />
    <Folder Include="temp\Text\" />
    <Folder Include="temp\Scripts\" />
    <Folder Include="temp\FTP\" />
    <Folder Include="docs\" />
  </ItemGroup>
  
  <ItemGroup>
    <Content Include="assets\images\adenlink.ico" />
    <Content Include="assets\fonts\*.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="assets\templates\*.*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  
  <!-- نسخ الملفات المطلوبة عند البناء -->
  <Target Name="AfterBuild">
    <ItemGroup>
      <ConfigFiles Include="config\**\*.*" />
      <AssetFiles Include="assets\**\*.*" />
    </ItemGroup>
    <Copy SourceFiles="@(ConfigFiles)" DestinationFolder="$(OutputPath)config\%(RecursiveDir)" />
    <Copy SourceFiles="@(AssetFiles)" DestinationFolder="$(OutputPath)assets\%(RecursiveDir)" />
  </Target>
</Project>
