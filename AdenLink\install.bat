@echo off
chcp 65001 >nul
echo ========================================
echo    تثبيت نظام AdenLink - عدن لنك
echo ========================================
echo.

REM التحقق من صلاحيات المدير
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo تحذير: يُنصح بتشغيل التثبيت كمدير للحصول على أفضل النتائج
    echo.
)

REM تحديد مجلد التثبيت
set "INSTALL_DIR=%ProgramFiles%\AdenLink"
echo مجلد التثبيت الافتراضي: %INSTALL_DIR%
echo.
echo هل تريد تغيير مجلد التثبيت؟ (Y/N)
set /p change_dir=
if /i "%change_dir%"=="Y" (
    set /p "INSTALL_DIR=أدخل مسار التثبيت الجديد: "
)

echo.
echo سيتم تثبيت AdenLink في: %INSTALL_DIR%
echo.

REM إنشاء مجلد التثبيت
echo إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%" 2>nul
    if %ERRORLEVEL% NEQ 0 (
        echo خطأ: لا يمكن إنشاء مجلد التثبيت
        echo يرجى تشغيل التثبيت كمدير أو اختيار مجلد آخر
        pause
        exit /b 1
    )
)

REM نسخ الملفات
echo نسخ ملفات البرنامج...
xcopy "bin\Release\*.*" "%INSTALL_DIR%\" /E /I /Y >nul
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: فشل في نسخ الملفات
    pause
    exit /b 1
)

REM إنشاء اختصار على سطح المكتب
echo إنشاء اختصار على سطح المكتب...
set "DESKTOP=%USERPROFILE%\Desktop"
set "SHORTCUT=%DESKTOP%\AdenLink.lnk"

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\AdenLink.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'AdenLink - نظام إدارة بطاقات الشحن الذكية'; $Shortcut.Save()}" >nul 2>&1

REM إنشاء اختصار في قائمة ابدأ
echo إنشاء اختصار في قائمة ابدأ...
set "STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%STARTMENU%\AdenLink" mkdir "%STARTMENU%\AdenLink"

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU%\AdenLink\AdenLink.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\AdenLink.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'AdenLink - نظام إدارة بطاقات الشحن الذكية'; $Shortcut.Save()}" >nul 2>&1

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU%\AdenLink\إلغاء التثبيت.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\uninstall.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'إلغاء تثبيت AdenLink'; $Shortcut.Save()}" >nul 2>&1

REM إنشاء ملف إلغاء التثبيت
echo إنشاء ملف إلغاء التثبيت...
(
echo @echo off
echo chcp 65001 ^>nul
echo echo ========================================
echo echo    إلغاء تثبيت نظام AdenLink - عدن لنك
echo echo ========================================
echo echo.
echo echo هل أنت متأكد من إلغاء تثبيت AdenLink؟ ^(Y/N^)
echo set /p confirm=
echo if /i "%%confirm%%"=="Y" ^(
echo     echo إلغاء تثبيت AdenLink...
echo     taskkill /f /im AdenLink.exe ^>nul 2^>^&1
echo     timeout /t 2 ^>nul
echo     del "%%USERPROFILE%%\Desktop\AdenLink.lnk" ^>nul 2^>^&1
echo     rmdir /s /q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\AdenLink" ^>nul 2^>^&1
echo     cd /d "%%TEMP%%"
echo     rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1
echo     echo تم إلغاء تثبيت AdenLink بنجاح
echo ^) else ^(
echo     echo تم إلغاء العملية
echo ^)
echo pause
) > "%INSTALL_DIR%\uninstall.bat"

REM تسجيل معلومات التثبيت
echo تسجيل معلومات التثبيت...
(
echo Installation Information
echo ========================
echo Install Date: %date% %time%
echo Install Path: %INSTALL_DIR%
echo Install User: %USERNAME%
echo Computer Name: %COMPUTERNAME%
echo OS Version: %OS%
echo Processor: %PROCESSOR_IDENTIFIER%
) > "%INSTALL_DIR%\InstallInfo.txt"

REM التحقق من متطلبات النظام
echo التحقق من متطلبات النظام...

REM التحقق من .NET Framework
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo تحذير: .NET Framework 4.7.2 أو أحدث مطلوب
    echo يرجى تحميله من موقع Microsoft
    echo.
)

REM إنشاء مجلدات البيانات
echo إنشاء مجلدات البيانات...
if not exist "%INSTALL_DIR%\database" mkdir "%INSTALL_DIR%\database"
if not exist "%INSTALL_DIR%\database\backup" mkdir "%INSTALL_DIR%\database\backup"
if not exist "%INSTALL_DIR%\temp" mkdir "%INSTALL_DIR%\temp"

REM تعيين صلاحيات المجلدات
echo تعيين صلاحيات المجلدات...
icacls "%INSTALL_DIR%\database" /grant Users:F >nul 2>&1
icacls "%INSTALL_DIR%\temp" /grant Users:F >nul 2>&1

echo.
echo ========================================
echo تم تثبيت AdenLink بنجاح!
echo ========================================
echo.
echo مجلد التثبيت: %INSTALL_DIR%
echo تم إنشاء اختصار على سطح المكتب
echo تم إنشاء اختصار في قائمة ابدأ
echo.
echo ملاحظات مهمة:
echo - تأكد من وجود .NET Framework 4.7.2 أو أحدث
echo - يُنصح بعمل نسخة احتياطية دورية من البيانات
echo - راجع دليل المستخدم في مجلد docs
echo.

echo هل تريد تشغيل AdenLink الآن؟ (Y/N)
set /p run_now=
if /i "%run_now%"=="Y" (
    start "" "%INSTALL_DIR%\AdenLink.exe"
)

echo.
echo شكراً لاستخدامك نظام AdenLink!
echo للدعم الفني: <EMAIL>
echo.
pause
