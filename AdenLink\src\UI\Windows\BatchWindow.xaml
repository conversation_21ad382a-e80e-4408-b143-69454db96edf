<Window x:Class="AdenLink.Windows.BatchWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنشاء دفعة جديدة - AdenLink"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardPanel}" Margin="10,10,10,5">
            <StackPanel>
                <TextBlock Text="إنشاء دفعة جديدة من البطاقات" Style="{StaticResource HeaderText}"/>
                <TextBlock Text="املأ البيانات التالية لإنشاء دفعة جديدة من بطاقات الشحن" 
                          Style="{StaticResource BodyText}"/>
            </StackPanel>
        </Border>

        <!-- النموذج -->
        <Border Grid.Row="1" Style="{StaticResource CardPanel}" Margin="10,5">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="10">
                    <!-- معلومات الدفعة -->
                    <TextBlock Text="معلومات الدفعة" Style="{StaticResource SubHeaderText}"/>
                    
                    <Label Content="اسم الدفعة:" Style="{StaticResource LabelText}"/>
                    <TextBox x:Name="BatchNameTextBox" Style="{StaticResource InputTextBox}" 
                             ToolTip="أدخل اسماً وصفياً للدفعة"/>

                    <Label Content="وصف الدفعة (اختياري):" Style="{StaticResource LabelText}"/>
                    <TextBox x:Name="BatchDescriptionTextBox" Style="{StaticResource InputTextBox}" 
                             Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                             ToolTip="أدخل وصفاً للدفعة (اختياري)"/>

                    <!-- إعدادات البطاقات -->
                    <TextBlock Text="إعدادات البطاقات" Style="{StaticResource SubHeaderText}" Margin="0,20,0,10"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <Label Content="عدد البطاقات:" Style="{StaticResource LabelText}"/>
                            <TextBox x:Name="CardCountTextBox" Style="{StaticResource InputTextBox}" 
                                     Text="10" ToolTip="عدد البطاقات المراد إنشاؤها"/>

                            <Label Content="قيمة البطاقة:" Style="{StaticResource LabelText}"/>
                            <TextBox x:Name="CardValueTextBox" Style="{StaticResource InputTextBox}" 
                                     Text="10" ToolTip="قيمة البطاقة الواحدة"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <Label Content="العملة:" Style="{StaticResource LabelText}"/>
                            <ComboBox x:Name="CurrencyComboBox" Style="{StaticResource InputComboBox}">
                                <ComboBoxItem Content="ريال" IsSelected="True"/>
                                <ComboBoxItem Content="دولار"/>
                                <ComboBoxItem Content="يورو"/>
                                <ComboBoxItem Content="دينار"/>
                            </ComboBox>

                            <Label Content="بادئة الكود:" Style="{StaticResource LabelText}"/>
                            <TextBox x:Name="CardPrefixTextBox" Style="{StaticResource InputTextBox}" 
                                     Text="AL" MaxLength="5" ToolTip="بادئة أكواد البطاقات"/>
                        </StackPanel>
                    </Grid>

                    <!-- إعدادات متقدمة -->
                    <Expander Header="إعدادات متقدمة" Margin="0,20,0,0" IsExpanded="False">
                        <StackPanel Margin="0,10">
                            <CheckBox x:Name="AutoExportCheckBox" Content="تصدير تلقائي بعد الإنشاء" 
                                      Margin="0,5" FontFamily="{StaticResource ArabicFont}"/>
                            
                            <CheckBox x:Name="AutoUploadCheckBox" Content="رفع تلقائي للخادم" 
                                      Margin="0,5" FontFamily="{StaticResource ArabicFont}"/>
                            
                            <CheckBox x:Name="AddToMikroTikCheckBox" Content="إضافة إلى MikroTik تلقائياً" 
                                      Margin="0,5" FontFamily="{StaticResource ArabicFont}"/>

                            <Label Content="تاريخ انتهاء الصلاحية (اختياري):" Style="{StaticResource LabelText}"/>
                            <DatePicker x:Name="ExpiryDatePicker" Style="{StaticResource InputTextBox}" 
                                        ToolTip="اترك فارغاً للبطاقات بدون انتهاء صلاحية"/>

                            <Label Content="ملاحظات إضافية:" Style="{StaticResource LabelText}"/>
                            <TextBox x:Name="NotesTextBox" Style="{StaticResource InputTextBox}" 
                                     Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                        </StackPanel>
                    </Expander>

                    <!-- معاينة -->
                    <TextBlock Text="معاينة" Style="{StaticResource SubHeaderText}" Margin="0,20,0,10"/>
                    <Border Background="{StaticResource BackgroundBrush}" BorderBrush="{StaticResource BorderBrush}" 
                            BorderThickness="1" CornerRadius="5" Padding="10">
                        <StackPanel>
                            <TextBlock x:Name="PreviewText" Style="{StaticResource BodyText}" 
                                       Text="سيتم إنشاء 10 بطاقات بقيمة 10 ريال لكل بطاقة"/>
                            <TextBlock x:Name="TotalValueText" Style="{StaticResource BodyText}" 
                                       Text="القيمة الإجمالية: 100 ريال" FontWeight="Bold"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- أزرار التحكم -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceBrush}" 
                BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="10">
                <Button x:Name="CreateButton" Content="إنشاء الدفعة" Style="{StaticResource PrimaryButton}" 
                        Click="CreateButton_Click" MinWidth="120"/>
                
                <Button x:Name="PreviewButton" Content="معاينة" Style="{StaticResource SecondaryButton}" 
                        Click="PreviewButton_Click" MinWidth="100"/>
                
                <Button x:Name="CancelButton" Content="إلغاء" Style="{StaticResource AccentButton}" 
                        Click="CancelButton_Click" MinWidth="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
