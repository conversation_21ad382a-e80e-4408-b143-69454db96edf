using System;
using System.Windows;
using System.Windows.Controls;
using AdenLink.Core;
using AdenLink.Database;

namespace AdenLink.Windows
{
    public partial class BatchWindow : Window
    {
        private DatabaseManager dbManager;
        private ConfigManager configManager;
        private CardGenerator cardGenerator;

        public BatchWindow()
        {
            InitializeComponent();
            InitializeWindow();
            LoadDefaultValues();
            UpdatePreview();
        }

        private void InitializeWindow()
        {
            try
            {
                // الحصول على المدراء من التطبيق الرئيسي
                dbManager = App.GetDatabaseManager();
                configManager = App.GetConfigManager();
                cardGenerator = new CardGenerator();

                // ربط أحداث التحديث
                CardCountTextBox.TextChanged += UpdatePreview;
                CardValueTextBox.TextChanged += UpdatePreview;
                CurrencyComboBox.SelectionChanged += UpdatePreview;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDefaultValues()
        {
            try
            {
                // تحميل القيم الافتراضية من الإعدادات
                CardValueTextBox.Text = configManager.DefaultCardValue.ToString();
                CardPrefixTextBox.Text = configManager.DefaultCardPrefix;
                
                // تحديد العملة الافتراضية
                string defaultCurrency = configManager.DefaultCurrency;
                foreach (ComboBoxItem item in CurrencyComboBox.Items)
                {
                    if (item.Content.ToString() == defaultCurrency)
                    {
                        item.IsSelected = true;
                        break;
                    }
                }

                // تحميل إعدادات التصدير والرفع
                AutoExportCheckBox.IsChecked = configManager.GetBoolSetting("AutoExport", false);
                AutoUploadCheckBox.IsChecked = configManager.FTPEnabled || configManager.SSHEnabled;
                AddToMikroTikCheckBox.IsChecked = configManager.MikroTikEnabled;

                // تعيين اسم افتراضي للدفعة
                BatchNameTextBox.Text = $"دفعة {DateTime.Now:yyyy-MM-dd HH:mm}";
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في تحميل القيم الافتراضية: {ex.Message}");
            }
        }

        private void UpdatePreview(object sender = null, EventArgs e = null)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!int.TryParse(CardCountTextBox.Text, out int cardCount) || cardCount <= 0)
                {
                    PreviewText.Text = "عدد البطاقات غير صحيح";
                    TotalValueText.Text = "";
                    return;
                }

                if (!decimal.TryParse(CardValueTextBox.Text, out decimal cardValue) || cardValue <= 0)
                {
                    PreviewText.Text = "قيمة البطاقة غير صحيحة";
                    TotalValueText.Text = "";
                    return;
                }

                string currency = ((ComboBoxItem)CurrencyComboBox.SelectedItem)?.Content?.ToString() ?? "ريال";
                decimal totalValue = cardCount * cardValue;

                // تحديث النص
                PreviewText.Text = $"سيتم إنشاء {cardCount:N0} بطاقة بقيمة {cardValue:N2} {currency} لكل بطاقة";
                TotalValueText.Text = $"القيمة الإجمالية: {totalValue:N2} {currency}";

                // تفعيل/تعطيل زر الإنشاء
                CreateButton.IsEnabled = cardCount > 0 && cardValue > 0 && !string.IsNullOrWhiteSpace(BatchNameTextBox.Text);
            }
            catch (Exception ex)
            {
                PreviewText.Text = "خطأ في المعاينة";
                TotalValueText.Text = "";
                dbManager?.LogOperation("خطأ", $"فشل في تحديث المعاينة: {ex.Message}");
            }
        }

        private void CreateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // تعطيل الأزرار أثناء الإنشاء
                CreateButton.IsEnabled = false;
                CreateButton.Content = "جاري الإنشاء...";

                // جمع البيانات
                string batchName = BatchNameTextBox.Text.Trim();
                int cardCount = int.Parse(CardCountTextBox.Text);
                decimal cardValue = decimal.Parse(CardValueTextBox.Text);
                string currency = ((ComboBoxItem)CurrencyComboBox.SelectedItem).Content.ToString();

                // إنشاء الدفعة
                var batch = cardGenerator.GenerateCardBatch(batchName, cardCount, cardValue, currency);

                // تنفيذ الإجراءات الإضافية
                if (AutoExportCheckBox.IsChecked == true)
                {
                    PerformAutoExport(batch);
                }

                if (AutoUploadCheckBox.IsChecked == true)
                {
                    PerformAutoUpload(batch);
                }

                if (AddToMikroTikCheckBox.IsChecked == true)
                {
                    AddToMikroTik(batch);
                }

                // عرض رسالة نجاح
                MessageBox.Show(
                    $"تم إنشاء الدفعة بنجاح!\n\nاسم الدفعة: {batchName}\nعدد البطاقات: {cardCount:N0}\nالقيمة الإجمالية: {(cardCount * cardValue):N2} {currency}",
                    "تم بنجاح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                // إغلاق النافذة مع إرجاع نتيجة إيجابية
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء الدفعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                
                // إعادة تفعيل الأزرار
                CreateButton.IsEnabled = true;
                CreateButton.Content = "إنشاء الدفعة";
            }
        }

        private bool ValidateInput()
        {
            try
            {
                // التحقق من اسم الدفعة
                if (string.IsNullOrWhiteSpace(BatchNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الدفعة", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    BatchNameTextBox.Focus();
                    return false;
                }

                // التحقق من عدد البطاقات
                if (!int.TryParse(CardCountTextBox.Text, out int cardCount) || cardCount <= 0 || cardCount > 10000)
                {
                    MessageBox.Show("عدد البطاقات يجب أن يكون رقماً صحيحاً بين 1 و 10000", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    CardCountTextBox.Focus();
                    return false;
                }

                // التحقق من قيمة البطاقة
                if (!decimal.TryParse(CardValueTextBox.Text, out decimal cardValue) || cardValue <= 0)
                {
                    MessageBox.Show("قيمة البطاقة يجب أن تكون رقماً موجباً", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    CardValueTextBox.Focus();
                    return false;
                }

                // التحقق من بادئة الكود
                if (string.IsNullOrWhiteSpace(CardPrefixTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال بادئة الكود", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    CardPrefixTextBox.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private void PerformAutoExport(CardGenerator.BatchInfo batch)
        {
            try
            {
                // سيتم تنفيذ التصدير التلقائي لاحقاً
                dbManager.LogOperation("تصدير تلقائي", $"تم تصدير الدفعة {batch.BatchName} تلقائياً");
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في التصدير التلقائي: {ex.Message}");
            }
        }

        private void PerformAutoUpload(CardGenerator.BatchInfo batch)
        {
            try
            {
                // سيتم تنفيذ الرفع التلقائي لاحقاً
                dbManager.LogOperation("رفع تلقائي", $"تم رفع الدفعة {batch.BatchName} تلقائياً");
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في الرفع التلقائي: {ex.Message}");
            }
        }

        private void AddToMikroTik(CardGenerator.BatchInfo batch)
        {
            try
            {
                // سيتم تنفيذ إضافة MikroTik لاحقاً
                dbManager.LogOperation("MikroTik", $"تم إضافة الدفعة {batch.BatchName} إلى MikroTik تلقائياً");
            }
            catch (Exception ex)
            {
                dbManager.LogOperation("خطأ", $"فشل في إضافة MikroTik: {ex.Message}");
            }
        }

        private void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // عرض معاينة مفصلة
                string batchName = BatchNameTextBox.Text.Trim();
                int cardCount = int.Parse(CardCountTextBox.Text);
                decimal cardValue = decimal.Parse(CardValueTextBox.Text);
                string currency = ((ComboBoxItem)CurrencyComboBox.SelectedItem).Content.ToString();
                string prefix = CardPrefixTextBox.Text.Trim();

                string previewMessage = $"معاينة الدفعة:\n\n" +
                    $"اسم الدفعة: {batchName}\n" +
                    $"عدد البطاقات: {cardCount:N0}\n" +
                    $"قيمة البطاقة: {cardValue:N2} {currency}\n" +
                    $"القيمة الإجمالية: {(cardCount * cardValue):N2} {currency}\n" +
                    $"بادئة الكود: {prefix}\n\n" +
                    $"مثال على كود البطاقة: {prefix}123456789012\n\n" +
                    $"هل تريد المتابعة؟";

                var result = MessageBox.Show(previewMessage, "معاينة الدفعة", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    CreateButton_Click(sender, e);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المعاينة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // حفظ الإعدادات المحدثة
                if (!string.IsNullOrWhiteSpace(CardPrefixTextBox.Text))
                {
                    configManager.SetSetting("DefaultCardPrefix", CardPrefixTextBox.Text.Trim());
                }

                if (decimal.TryParse(CardValueTextBox.Text, out decimal value) && value > 0)
                {
                    configManager.SetDecimalSetting("DefaultCardValue", value);
                }

                string selectedCurrency = ((ComboBoxItem)CurrencyComboBox.SelectedItem)?.Content?.ToString();
                if (!string.IsNullOrEmpty(selectedCurrency))
                {
                    configManager.SetSetting("DefaultCurrency", selectedCurrency);
                }

                configManager.SetBoolSetting("AutoExport", AutoExportCheckBox.IsChecked == true);
            }
            catch (Exception ex)
            {
                dbManager?.LogOperation("خطأ", $"فشل في حفظ الإعدادات عند إغلاق النافذة: {ex.Message}");
            }

            base.OnClosed(e);
        }
    }
}
