<Window x:Class="AdenLink.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="AdenLink - عدن لنك | نظام إدارة بطاقات الشحن الذكية"
        Height="800" Width="1200"
        MinHeight="600" MinWidth="900"
        WindowStartupLocation="CenterScreen"
        Icon="../../assets/images/adenlink.ico"
        FlowDirection="RightToLeft">

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط القوائم -->
        <Menu Grid.Row="0" Style="{StaticResource MenuStyle}">
            <MenuItem Header="ملف">
                <MenuItem Header="جديد" Click="NewBatch_Click">
                    <MenuItem.Icon>
                        <Image Source="../../assets/images/new.png" Width="16" Height="16"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="فتح" Click="OpenBatch_Click">
                    <MenuItem.Icon>
                        <Image Source="../../assets/images/open.png" Width="16" Height="16"/>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator/>
                <MenuItem Header="تصدير">
                    <MenuItem Header="تصدير PDF" Click="ExportPDF_Click"/>
                    <MenuItem Header="تصدير Excel" Click="ExportExcel_Click"/>
                    <MenuItem Header="تصدير صور" Click="ExportImages_Click"/>
                    <MenuItem Header="تصدير نص" Click="ExportText_Click"/>
                </MenuItem>
                <Separator/>
                <MenuItem Header="إعدادات" Click="Settings_Click">
                    <MenuItem.Icon>
                        <Image Source="../../assets/images/settings.png" Width="16" Height="16"/>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator/>
                <MenuItem Header="خروج" Click="Exit_Click"/>
            </MenuItem>

            <MenuItem Header="البطاقات">
                <MenuItem Header="إنشاء دفعة جديدة" Click="NewBatch_Click"/>
                <MenuItem Header="عرض جميع الدفعات" Click="ViewBatches_Click"/>
                <MenuItem Header="البحث عن بطاقة" Click="SearchCard_Click"/>
                <Separator/>
                <MenuItem Header="أرشيف البطاقات" Click="ViewArchive_Click"/>
            </MenuItem>

            <MenuItem Header="الشبكة">
                <MenuItem Header="اتصال MikroTik" Click="ConnectMikroTik_Click"/>
                <MenuItem Header="رفع FTP" Click="UploadFTP_Click"/>
                <MenuItem Header="رفع SSH" Click="UploadSSH_Click"/>
                <Separator/>
                <MenuItem Header="اختبار الاتصال" Click="TestConnection_Click"/>
            </MenuItem>

            <MenuItem Header="أدوات">
                <MenuItem Header="نسخ احتياطي" Click="Backup_Click"/>
                <MenuItem Header="استعادة" Click="Restore_Click"/>
                <Separator/>
                <MenuItem Header="تنظيف الملفات المؤقتة" Click="CleanTemp_Click"/>
                <MenuItem Header="عرض السجلات" Click="ViewLogs_Click"/>
            </MenuItem>

            <MenuItem Header="مساعدة">
                <MenuItem Header="دليل المستخدم" Click="UserGuide_Click"/>
                <MenuItem Header="حول البرنامج" Click="About_Click"/>
            </MenuItem>
        </Menu>

        <!-- شريط الأدوات -->
        <ToolBar Grid.Row="1" Style="{StaticResource ToolBarStyle}">
            <Button Style="{StaticResource PrimaryButton}" Click="NewBatch_Click" ToolTip="إنشاء دفعة جديدة">
                <StackPanel Orientation="Horizontal">
                    <Image Source="../../assets/images/new.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="جديد"/>
                </StackPanel>
            </Button>
            
            <Button Style="{StaticResource SecondaryButton}" Click="ViewBatches_Click" ToolTip="عرض جميع الدفعات">
                <StackPanel Orientation="Horizontal">
                    <Image Source="../../assets/images/view.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="عرض"/>
                </StackPanel>
            </Button>
            
            <Separator/>
            
            <Button Style="{StaticResource AccentButton}" Click="ExportPDF_Click" ToolTip="تصدير PDF">
                <StackPanel Orientation="Horizontal">
                    <Image Source="../../assets/images/pdf.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="PDF"/>
                </StackPanel>
            </Button>
            
            <Button Style="{StaticResource AccentButton}" Click="ExportExcel_Click" ToolTip="تصدير Excel">
                <StackPanel Orientation="Horizontal">
                    <Image Source="../../assets/images/excel.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Excel"/>
                </StackPanel>
            </Button>
            
            <Separator/>
            
            <Button Style="{StaticResource PrimaryButton}" Click="ConnectMikroTik_Click" ToolTip="اتصال MikroTik">
                <StackPanel Orientation="Horizontal">
                    <Image Source="../../assets/images/network.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="MikroTik"/>
                </StackPanel>
            </Button>
            
            <Button Style="{StaticResource SecondaryButton}" Click="Settings_Click" ToolTip="الإعدادات">
                <StackPanel Orientation="Horizontal">
                    <Image Source="../../assets/images/settings.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="إعدادات"/>
                </StackPanel>
            </Button>
        </ToolBar>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- اللوحة الجانبية -->
            <Border Grid.Column="0" Style="{StaticResource CardPanel}" Margin="0,0,10,0">
                <StackPanel>
                    <!-- معلومات سريعة -->
                    <TextBlock Text="معلومات سريعة" Style="{StaticResource SubHeaderText}"/>
                    
                    <Border Background="{StaticResource PrimaryBrush}" CornerRadius="5" Padding="10" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="إجمالي البطاقات" Foreground="White" FontWeight="Bold"/>
                            <TextBlock x:Name="TotalCardsText" Text="0" Foreground="White" FontSize="24" FontWeight="Bold"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Background="{StaticResource SuccessBrush}" CornerRadius="5" Padding="10" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="البطاقات المستخدمة" Foreground="White" FontWeight="Bold"/>
                            <TextBlock x:Name="UsedCardsText" Text="0" Foreground="White" FontSize="24" FontWeight="Bold"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Background="{StaticResource AccentBrush}" CornerRadius="5" Padding="10" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="البطاقات المتاحة" Foreground="White" FontWeight="Bold"/>
                            <TextBlock x:Name="AvailableCardsText" Text="0" Foreground="White" FontSize="24" FontWeight="Bold"/>
                        </StackPanel>
                    </Border>

                    <!-- إجراءات سريعة -->
                    <TextBlock Text="إجراءات سريعة" Style="{StaticResource SubHeaderText}" Margin="0,20,0,10"/>
                    
                    <Button Style="{StaticResource PrimaryButton}" Click="NewBatch_Click" Margin="0,0,0,5">
                        <TextBlock Text="إنشاء دفعة جديدة"/>
                    </Button>
                    
                    <Button Style="{StaticResource SecondaryButton}" Click="SearchCard_Click" Margin="0,0,0,5">
                        <TextBlock Text="البحث عن بطاقة"/>
                    </Button>
                    
                    <Button Style="{StaticResource AccentButton}" Click="ViewArchive_Click" Margin="0,0,0,5">
                        <TextBlock Text="عرض الأرشيف"/>
                    </Button>

                    <!-- حالة الاتصال -->
                    <TextBlock Text="حالة الاتصال" Style="{StaticResource SubHeaderText}" Margin="0,20,0,10"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <Ellipse x:Name="MikroTikStatus" Width="10" Height="10" Fill="Red" Margin="0,0,5,0"/>
                        <TextBlock Text="MikroTik" Style="{StaticResource BodyText}"/>
                    </StackPanel>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <Ellipse x:Name="FTPStatus" Width="10" Height="10" Fill="Red" Margin="0,0,5,0"/>
                        <TextBlock Text="FTP Server" Style="{StaticResource BodyText}"/>
                    </StackPanel>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <Ellipse x:Name="SSHStatus" Width="10" Height="10" Fill="Red" Margin="0,0,5,0"/>
                        <TextBlock Text="SSH Server" Style="{StaticResource BodyText}"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- المنطقة الرئيسية -->
            <TabControl Grid.Column="1" x:Name="MainTabControl">
                <TabItem Header="الدفعات" x:Name="BatchesTab">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- شريط البحث والفلترة -->
                        <Border Grid.Row="0" Style="{StaticResource CardPanel}" Margin="0,0,0,10">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="البحث:" Style="{StaticResource LabelText}" VerticalAlignment="Center"/>
                                <TextBox x:Name="SearchTextBox" Style="{StaticResource InputTextBox}" Width="200" Margin="5,0"/>
                                <Button Style="{StaticResource PrimaryButton}" Click="Search_Click" Margin="5,0">
                                    <TextBlock Text="بحث"/>
                                </Button>
                                <Button Style="{StaticResource SecondaryButton}" Click="ClearSearch_Click" Margin="5,0">
                                    <TextBlock Text="مسح"/>
                                </Button>
                            </StackPanel>
                        </Border>

                        <!-- جدول الدفعات -->
                        <DataGrid Grid.Row="1" x:Name="BatchesDataGrid" Style="{StaticResource DataGridStyle}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="معرف الدفعة" Binding="{Binding BatchId}" Width="150"/>
                                <DataGridTextColumn Header="اسم الدفعة" Binding="{Binding BatchName}" Width="200"/>
                                <DataGridTextColumn Header="عدد البطاقات" Binding="{Binding TotalCards}" Width="100"/>
                                <DataGridTextColumn Header="قيمة البطاقة" Binding="{Binding CardValue}" Width="100"/>
                                <DataGridTextColumn Header="العملة" Binding="{Binding Currency}" Width="80"/>
                                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                                <DataGridTemplateColumn Header="إجراءات" Width="150">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Content="عرض" Style="{StaticResource PrimaryButton}" 
                                                        Click="ViewBatch_Click" Tag="{Binding BatchId}" Margin="2"/>
                                                <Button Content="تصدير" Style="{StaticResource AccentButton}" 
                                                        Click="ExportBatch_Click" Tag="{Binding BatchId}" Margin="2"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </TabItem>

                <TabItem Header="البطاقات" x:Name="CardsTab">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- معلومات الدفعة المحددة -->
                        <Border Grid.Row="0" Style="{StaticResource CardPanel}" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock x:Name="SelectedBatchInfo" Text="اختر دفعة لعرض البطاقات" 
                                          Style="{StaticResource SubHeaderText}"/>
                            </StackPanel>
                        </Border>

                        <!-- جدول البطاقات -->
                        <DataGrid Grid.Row="1" x:Name="CardsDataGrid" Style="{StaticResource DataGridStyle}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="كود البطاقة" Binding="{Binding CardCode}" Width="150"/>
                                <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="120"/>
                                <DataGridTextColumn Header="كلمة المرور" Binding="{Binding Password}" Width="120"/>
                                <DataGridTextColumn Header="القيمة" Binding="{Binding Value}" Width="80"/>
                                <DataGridTextColumn Header="العملة" Binding="{Binding Currency}" Width="60"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </TabItem>

                <TabItem Header="الإحصائيات" x:Name="StatsTab">
                    <Border Style="{StaticResource CardPanel}">
                        <StackPanel>
                            <TextBlock Text="إحصائيات النظام" Style="{StaticResource HeaderText}"/>
                            <TextBlock Text="سيتم إضافة الإحصائيات والرسوم البيانية هنا" 
                                      Style="{StaticResource BodyText}" Margin="0,20"/>
                        </StackPanel>
                    </Border>
                </TabItem>
            </TabControl>
        </Grid>

        <!-- شريط الحالة -->
        <StatusBar Grid.Row="3" Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock x:Name="StatusText" Text="جاهز"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="المستخدم: "/>
                    <TextBlock x:Name="CurrentUserText" Text="{Binding Source={x:Static Environment.UserName}}"/>
                    <TextBlock Text=" | "/>
                    <TextBlock x:Name="CurrentTimeText" Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat=HH:mm:ss}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
